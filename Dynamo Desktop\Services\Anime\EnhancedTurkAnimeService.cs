using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text.Json;
using System.Threading.Tasks;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Scrapers.Anime;

namespace Dynamo_Desktop.Services.Anime
{
    public class EnhancedTurkAnimeService : IAnimeService, IDisposable
    {
        private readonly EnhancedTurkAnimeScraper _scraper;
        private readonly object _lock = new object();
        private bool _disposed = false;

        public EnhancedTurkAnimeService()
        {
            _scraper = new EnhancedTurkAnimeScraper();
        }

        public async Task<List<PopularAnime>> PopularAnime(int Page = 1)
        {
            // For TurkAnime, popular anime is the same as recent anime
            return await RecentAnime(Page);
        }

        public async Task<List<PopularAnime>> RecentAnime(int Page = 1)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedTurkAnimeService));

            Debug.WriteLine($"EnhancedTurkAnimeService: Fetching recent anime for page {Page}");
            
            try
            {
                var scraperResult = await _scraper.RecentAnime(Page);
                Debug.WriteLine($"EnhancedTurkAnimeService: Scraper returned data length: {scraperResult?.Length ?? 0}");
                
                if (string.IsNullOrWhiteSpace(scraperResult))
                {
                    Debug.WriteLine("EnhancedTurkAnimeService: Empty result from scraper");
                    return new List<PopularAnime>();
                }
                
                var result = JsonSerializer.Deserialize<List<PopularAnime>>(scraperResult);
                if (result == null)
                {
                    Debug.WriteLine("EnhancedTurkAnimeService: Failed to deserialize recent anime");
                    return new List<PopularAnime>();
                }
                
                Debug.WriteLine($"EnhancedTurkAnimeService: Successfully found {result.Count} recent anime");
                return result;
            }
            catch (JsonException ex)
            {
                Debug.WriteLine($"EnhancedTurkAnimeService: JSON deserialization error in RecentAnime: {ex.Message}");
                return new List<PopularAnime>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"EnhancedTurkAnimeService: Exception in RecentAnime: {ex.Message}");
                Debug.WriteLine($"EnhancedTurkAnimeService: Stack trace: {ex.StackTrace}");
                return new List<PopularAnime>();
            }
        }

        public async Task<List<PopularAnime>> Search(string Query, int Page = 1)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedTurkAnimeService));

            if (string.IsNullOrWhiteSpace(Query))
            {
                Debug.WriteLine("EnhancedTurkAnimeService: Empty search query provided");
                return new List<PopularAnime>();
            }

            Debug.WriteLine($"EnhancedTurkAnimeService: Searching for '{Query}' on page {Page}");

            try
            {
                var scraperResult = await _scraper.Search(Query);
                Debug.WriteLine($"EnhancedTurkAnimeService: Search scraper returned data length: {scraperResult?.Length ?? 0}");

                if (string.IsNullOrWhiteSpace(scraperResult))
                {
                    Debug.WriteLine("EnhancedTurkAnimeService: Empty search result from scraper");
                    return new List<PopularAnime>();
                }

                var result = JsonSerializer.Deserialize<List<PopularAnime>>(scraperResult);
                if (result == null)
                {
                    Debug.WriteLine("EnhancedTurkAnimeService: Failed to deserialize search results");
                    return new List<PopularAnime>();
                }

                Debug.WriteLine($"EnhancedTurkAnimeService: Successfully found {result.Count} search results");
                return result;
            }
            catch (JsonException ex)
            {
                Debug.WriteLine($"EnhancedTurkAnimeService: JSON deserialization error in Search: {ex.Message}");
                return new List<PopularAnime>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"EnhancedTurkAnimeService: Exception in Search: {ex.Message}");
                Debug.WriteLine($"EnhancedTurkAnimeService: Stack trace: {ex.StackTrace}");
                return new List<PopularAnime>();
            }
        }

        public async Task<AnimeInfo> Info(string Query)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedTurkAnimeService));

            if (string.IsNullOrWhiteSpace(Query))
            {
                Debug.WriteLine("EnhancedTurkAnimeService: Empty anime ID provided for Info");
                return new AnimeInfo();
            }

            Debug.WriteLine($"EnhancedTurkAnimeService: Fetching info for '{Query}'");
            
            try
            {
                var scraperResult = await _scraper.AnimeInfo(Query);
                Debug.WriteLine($"EnhancedTurkAnimeService: Info scraper returned data length: {scraperResult?.Length ?? 0}");
                
                if (string.IsNullOrWhiteSpace(scraperResult))
                {
                    Debug.WriteLine("EnhancedTurkAnimeService: Empty info result from scraper");
                    return new AnimeInfo { Title = Query }; // Return at least the query as title
                }
                
                var result = JsonSerializer.Deserialize<AnimeInfo>(scraperResult);
                if (result == null)
                {
                    Debug.WriteLine("EnhancedTurkAnimeService: Failed to deserialize anime info");
                    return new AnimeInfo { Title = Query };
                }
                
                Debug.WriteLine($"EnhancedTurkAnimeService: Successfully found anime info with {result.Episodes?.Count ?? 0} episodes");
                return result;
            }
            catch (JsonException ex)
            {
                Debug.WriteLine($"EnhancedTurkAnimeService: JSON deserialization error in Info: {ex.Message}");
                return new AnimeInfo { Title = Query };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"EnhancedTurkAnimeService: Exception in Info: {ex.Message}");
                Debug.WriteLine($"EnhancedTurkAnimeService: Stack trace: {ex.StackTrace}");
                return new AnimeInfo { Title = Query };
            }
        }

        public async Task<List<AnimeStreamingLinks>> StreamingLinks(string Query, int Episode = 1)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedTurkAnimeService));

            if (string.IsNullOrWhiteSpace(Query))
            {
                Debug.WriteLine("EnhancedTurkAnimeService: Empty anime ID provided for StreamingLinks");
                return new List<AnimeStreamingLinks>();
            }

            Debug.WriteLine($"EnhancedTurkAnimeService: Fetching streaming links for '{Query}' episode {Episode}");
            
            try
            {
                var scraperResult = await _scraper.EpisodeStreamLinks(Query, Episode);
                Debug.WriteLine($"EnhancedTurkAnimeService: Streaming links scraper returned data length: {scraperResult?.Length ?? 0}");
                
                if (string.IsNullOrWhiteSpace(scraperResult))
                {
                    Debug.WriteLine("EnhancedTurkAnimeService: Empty streaming links result from scraper");
                    return new List<AnimeStreamingLinks>();
                }
                
                var result = JsonSerializer.Deserialize<List<AnimeStreamingLinks>>(scraperResult);
                if (result == null)
                {
                    Debug.WriteLine("EnhancedTurkAnimeService: Failed to deserialize streaming links");
                    return new List<AnimeStreamingLinks>();
                }
                
                Debug.WriteLine($"EnhancedTurkAnimeService: Successfully found {result.Count} streaming links");
                foreach (var link in result)
                {
                    Debug.WriteLine($"  - Quality: {link.Quality}, Source: {(string.IsNullOrEmpty(link.Source) ? "N/A" : link.Source.Substring(0, Math.Min(50, link.Source.Length)))}...");
                }
                
                return result;
            }
            catch (JsonException ex)
            {
                Debug.WriteLine($"EnhancedTurkAnimeService: JSON deserialization error in StreamingLinks: {ex.Message}");
                return new List<AnimeStreamingLinks>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"EnhancedTurkAnimeService: Exception in StreamingLinks: {ex.Message}");
                Debug.WriteLine($"EnhancedTurkAnimeService: Stack trace: {ex.StackTrace}");
                return new List<AnimeStreamingLinks>();
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _scraper?.Dispose();
                }
                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~EnhancedTurkAnimeService()
        {
            Dispose(false);
        }
    }
}
