using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Services.Anime;

namespace TurkAnimeDiagnostic
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== TurkAnime Metadata Diagnostic Tool ===");
            Console.WriteLine("Testing TurkAnime metadata extraction...\n");
            
            await TestTurkAnime();
            
            Console.WriteLine("\n=== Diagnostic Complete ===");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
        
        static async Task TestTurkAnime()
        {
            Console.WriteLine("--- Testing TurkAnime Provider ---");
            
            try
            {
                var service = new TurkAnimeService();
                
                // Step 1: Test recent anime
                Console.WriteLine("Step 1: Getting recent anime from TurkAnime...");
                var recentAnime = await service.RecentAnime(1);
                
                if (recentAnime == null || recentAnime.Count == 0)
                {
                    Console.WriteLine("❌ No recent anime found from TurkAnime");
                    return;
                }
                
                Console.WriteLine($"✅ Found {recentAnime.Count} recent anime from TurkAnime");
                
                // Show first few anime with their metadata
                Console.WriteLine("Recent anime list:");
                foreach (var anime in recentAnime.Take(5))
                {
                    Console.WriteLine($"  - Title: {anime.Title}");
                    Console.WriteLine($"    ID: {anime.AnimeId}");
                    Console.WriteLine($"    Image: {(string.IsNullOrEmpty(anime.Image) ? "❌ Missing" : "✅ " + anime.Image)}");
                    Console.WriteLine($"    Episode: {anime.Episode}");
                    Console.WriteLine();
                }
                
                // Test with first anime
                var testAnime = recentAnime[0];
                Console.WriteLine($"Testing detailed info with: '{testAnime.Title}' (ID: {testAnime.AnimeId})");
                
                // Step 2: Test anime info
                Console.WriteLine($"Step 2: Getting anime info for {testAnime.AnimeId}...");
                var animeInfo = await service.Info(testAnime.AnimeId);
                
                if (animeInfo == null)
                {
                    Console.WriteLine($"❌ Failed to get anime info for {testAnime.AnimeId}");
                    return;
                }
                
                if (animeInfo.Episodes == null || animeInfo.Episodes.Count == 0)
                {
                    Console.WriteLine($"❌ No episodes found for {testAnime.AnimeId}");
                    return;
                }
                
                Console.WriteLine($"✅ Found anime info with {animeInfo.Episodes.Count} episodes");
                Console.WriteLine($"   Title: {animeInfo.Title}");
                Console.WriteLine($"   Description: {(string.IsNullOrEmpty(animeInfo.Description) ? "❌ Missing" : "✅ " + animeInfo.Description.Substring(0, Math.Min(100, animeInfo.Description.Length)) + "...")}");
                Console.WriteLine($"   Image: {(string.IsNullOrEmpty(animeInfo.Image) ? "❌ Missing" : "✅ " + animeInfo.Image)}");
                Console.WriteLine($"   Episode Count: {animeInfo.EpisodeCount}");
                
                // Step 3: Test streaming links
                Console.WriteLine($"Step 3: Getting streaming links for episode 1...");
                var streamingLinks = await service.StreamingLinks(testAnime.AnimeId, 1);
                
                if (streamingLinks == null || streamingLinks.Count == 0)
                {
                    Console.WriteLine($"❌ No streaming links found for {testAnime.AnimeId} episode 1");
                    Console.WriteLine($"   This is why you're getting 'no streams available' error.");
                    
                    // Let's test the URL directly
                    Console.WriteLine($"   Testing episode URL directly...");
                    await TestEpisodeUrlDirectly(testAnime.AnimeId);
                    return;
                }
                
                Console.WriteLine($"✅ Found {streamingLinks.Count} streaming links for episode 1");
                await TestStreamingLinks(streamingLinks);
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error testing TurkAnime: {ex.Message}");
                Console.WriteLine($"   Stack trace: {ex.StackTrace}");
            }
        }
        
        static async Task TestEpisodeUrlDirectly(string animeId)
        {
            try
            {
                string episodeUrl = $"https://www.turkanime.co/video/{animeId}-1";
                Console.WriteLine($"   Testing URL: {episodeUrl}");
                
                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                
                var response = await httpClient.GetAsync(episodeUrl);
                Console.WriteLine($"   Response status: {response.StatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"   Page content length: {content.Length} characters");
                    
                    // Check for common video indicators
                    bool hasVideo = content.Contains("video") || content.Contains("iframe") || 
                                   content.Contains(".mp4") || content.Contains(".m3u8");
                    Console.WriteLine($"   Contains video indicators: {hasVideo}");
                    
                    if (content.Contains("404") || content.Contains("Not Found"))
                    {
                        Console.WriteLine($"   ⚠️  Episode page not found - anime might not have episodes yet");
                    }
                }
                else
                {
                    Console.WriteLine($"   ❌ Episode page not accessible");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Error testing episode URL: {ex.Message}");
            }
        }
        
        static async Task TestStreamingLinks(List<AnimeStreamingLinks> links)
        {
            Console.WriteLine("Step 4: Testing streaming link accessibility...");
            
            foreach (var link in links)
            {
                Console.WriteLine($"   Quality: {link.Quality}");
                Console.WriteLine($"   Source: {link.Source}");
                
                if (string.IsNullOrWhiteSpace(link.Source))
                {
                    Console.WriteLine($"   ❌ Empty source URL");
                    continue;
                }
                
                if (!IsValidUrl(link.Source))
                {
                    Console.WriteLine($"   ❌ Invalid URL format");
                    continue;
                }
                
                bool accessible = await TestStreamingUrl(link.Source);
                Console.WriteLine($"   Accessible: {(accessible ? "✅ Yes" : "❌ No")}");
                Console.WriteLine();
            }
        }
        
        static async Task<bool> TestStreamingUrl(string url)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10);
                httpClient.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                
                var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Head, url));
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }
        
        static bool IsValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out Uri result) 
                && (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }
    }
}
