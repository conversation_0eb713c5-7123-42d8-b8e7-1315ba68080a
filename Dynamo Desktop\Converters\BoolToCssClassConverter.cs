using System;
using System.Collections.Generic;
using System.Globalization;
using Avalonia.Data.Converters;

namespace Dynamo_Desktop.Converters;

public class BoolToCssClassConverter : IMultiValueConverter
{
    public object Convert(IList<object> values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values == null || values.Count == 0)
            return string.Empty;
            
        var boolValue = values[0] as bool? ?? false;
        var cssClass = parameter?.ToString() ?? string.Empty;
        
        return boolValue ? cssClass : string.Empty;
    }
}