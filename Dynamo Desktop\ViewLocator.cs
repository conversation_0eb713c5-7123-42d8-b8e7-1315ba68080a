using Avalonia.Controls;
using Avalonia.Controls.Templates;
using Dynamo_Desktop.ViewModels;
using System;

namespace Dynamo_Desktop
{
    public class ViewLocator : IDataTemplate
    {
        public bool Match(object data)
        {
            return data is ViewModelBase;
        }

        Control ITemplate<object, Control>.Build(object data)
        {
            var name = data.GetType().FullName!.Replace("ViewModel", "View");
            var type = Type.GetType(name);

            if (type != null)
            {
                return (Control)Activator.CreateInstance(type)!;
            }

            return new TextBlock { Text = "Not Found: " + name };
        }
    }
}