﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <ItemGroup>
    <AvaloniaResource Include="Assets\**" />
  </ItemGroup>

  <ItemGroup>
    <TrimmerRootAssembly Include="Avalonia.Themes.Fluent" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AsyncImageLoader.Avalonia" Version="3.2.1" />
    <PackageReference Include="Avalonia" Version="11.1.0-beta2" />
    <PackageReference Include="Avalonia.Controls.ItemsRepeater" Version="11.1.0-beta2" />
    <PackageReference Include="Avalonia.Desktop" Version="11.1.0-beta2" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.1.0-beta2" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.1.0-beta2" />
    <PackageReference Include="CSharpFunctionalExtensions" Version="2.42.0" />
    <PackageReference Include="FluentAvaloniaUI" Version="2.1.0-preview4" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.61" />
    <PackageReference Include="MessageBox.Avalonia" Version="3.1.5.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PuppeteerExtraSharp" Version="2.0.0" />
    <PackageReference Include="PuppeteerSharp" Version="18.0.1" />
    <PackageReference Include="ReactiveUI.Fody" Version="19.5.41" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.0" />
    <PackageReference Include="Polly" Version="8.2.0" />
    <!--<PackageReference Include="XamlNameReferenceGenerator" Version="1.6.1" />-->
  </ItemGroup>


    
  <ItemGroup>
    <ProjectReference Include="..\Avalonia.GIF\AvaloniaGif\AvaloniaGif.csproj" />
  </ItemGroup>
	<ItemGroup>
		<Content Include="settings.json" CopyToOutputDirectory="PreserveNewest" CopyToPublishDirectory="PreserveNewest" />
	</ItemGroup>
  <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
    <Content Include="mpv\**" CopyToOutputDirectory="PreserveNewest" CopyToPublishDirectory="PreserveNewest"/>
  </ItemGroup>
</Project>
