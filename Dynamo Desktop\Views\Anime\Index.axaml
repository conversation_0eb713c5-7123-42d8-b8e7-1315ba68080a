<UserControl
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Dynamo_Desktop.Views.Anime.Index"
    x:DataType="vm:IndexViewModel"
    xmlns="https://github.com/avaloniaui"
    xmlns:anime="using:Dynamo_Desktop.Models.Anime"
    xmlns:asyncImageLoader="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ext="using:Dynamo_Desktop.Converters"
    xmlns:fluent="using:FluentAvalonia.UI.Controls"
    xmlns:loaders="using:AsyncImageLoader.Loaders"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:subview="using:Dynamo_Desktop.Views.Anime.Subviews"
    xmlns:vm="using:Dynamo_Desktop.ViewModels.Anime"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <UserControl.Resources>
        <ext:EqualityConverter x:Key="EqualityConverter" />
    </UserControl.Resources>
    <!--  Controls  -->
    <Grid>
        <ProgressBar
            IsIndeterminate="True"
            IsVisible="{Binding DataLoading}"
            VerticalAlignment="Top"
            Width="1000" />
        <!--  Controls  -->
        <Grid Margin="0,40,0,0" ZIndex="1">
            <!--  Sort Selection  -->
            <ComboBox
                HorizontalAlignment="Right"
                ItemsSource="{Binding SortOptions}"
                Margin="0,-39,150,0"
                SelectedItem="{Binding Sort}" />
            <!--  SORT TITLE  -->
            <TextBlock
                FontSize="35"
                Margin="25"
                Text="{Binding Sort}" />
            <!--  Search  -->
            <Border
                Height="10"
                HorizontalAlignment="Right"
                Margin="0,47,20,0"
                VerticalAlignment="Top"
                Width="300">
                <TextBox Text="{Binding SearchTerm}" Watermark="Search Providers" />
            </Border>
            <!--  Pagination  -->

            <StackPanel
                Margin="30,85,0,0"
                Orientation="Horizontal"
                VerticalAlignment="Top">
                <Button Command="{Binding PrevPage}" Content="Prev" />
                <Button Content="{Binding Page}" IsEnabled="False" />
                <Button Command="{Binding NextPage}" Content="Next" />
            </StackPanel>
        </Grid>

        <TabControl x:Name="Tab">
            <!--  TurkAnime  -->
            <TabItem Header="TurkAnime">
                <subview:IndexSubView Provider="TurkAnime" />
            </TabItem>

        </TabControl>

    </Grid>
</UserControl>
