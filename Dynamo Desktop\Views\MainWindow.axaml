<Window
    x:Class="Dynamo_Desktop.Views.MainWindow"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ext="using:Dynamo_Desktop.Converters"
    xmlns:fluent="using:FluentAvalonia.UI.Controls"
    xmlns:local="using:Dynamo_Desktop"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:Dynamo_Desktop.ViewModels"
    Title="Dynamo Desktop"
    MinWidth="1000"
    MinHeight="450"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="vm:MainWindowViewModel"
    ExtendClientAreaToDecorationsHint="False"
    Icon="/Assets/Images/Dynamo.png"
    TransparencyLevelHint="Mica"
    mc:Ignorable="d">
    <Window.Resources />
    <Design.DataContext>
        <!--
            This only sets the DataContext for the previewer in an IDE,
            to set the actual DataContext for runtime, set the DataContext property in code (look at App.axaml.cs)
        -->
        <vm:MainWindowViewModel  />
    </Design.DataContext>
    <local:AcrylicPanel>
        
        <!--Dont need this on windows-->
        
        <!--  Search Bar  -->
        <Grid>
            <!--<TextBlock
                Margin="60,20,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Text="Dynamo Desktop" />-->
        </Grid>
        <!--  -App title  -->


        <!--  Main Content  -->
        <Grid>
            <fluent:NavigationView
                x:Name="nvSample"
                BackRequested="BackRequested"
                IsBackButtonVisible="True"
                IsBackEnabled="True"
                IsPaneOpen="False"
                IsPaneToggleButtonVisible="False"
                IsSettingsVisible="False"
                OpenPaneLength="72"
                PaneDisplayMode="Left"
                PropertyChanged="NavigationItemChanged">
                <fluent:NavigationView.MenuItems>
                    <fluent:NavigationViewItem
                        x:Name="AnimePage"
                        Content="Anime"
                        Tag="Anime">
                        <fluent:NavigationViewItem.IconSource>
                            <fluent:ImageIconSource Source="/Assets/Images/anime-logo.png" />
                        </fluent:NavigationViewItem.IconSource>
                    </fluent:NavigationViewItem>
                    <fluent:NavigationViewItem Content="Hentai" Tag="Hentai">
                        <fluent:NavigationViewItem.IconSource>
                            <fluent:ImageIconSource Source="/Assets/Images/book-logo.png" />
                        </fluent:NavigationViewItem.IconSource>
                    </fluent:NavigationViewItem>
                    <fluent:NavigationViewItem Content="Settings" Tag="Settings">
                        <fluent:NavigationViewItem.IconSource>
                            <fluent:ImageIconSource Source="/Assets/Images/icons8-settings-48.png" />
                        </fluent:NavigationViewItem.IconSource>
                    </fluent:NavigationViewItem>
                </fluent:NavigationView.MenuItems>
                <Panel>
												<fluent:Frame x:Name="ContentFrame" Navigating="Frame_Navigating"  />
                </Panel>

            </fluent:NavigationView>
        </Grid>
    </local:AcrylicPanel>
</Window>
