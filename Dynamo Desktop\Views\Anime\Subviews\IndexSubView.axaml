<UserControl
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:Class="Dynamo_Desktop.Views.Anime.Subviews.IndexSubView"
    x:DataType="vm:IndexViewModel"
    xmlns="https://github.com/avaloniaui"
    xmlns:anime="using:Dynamo_Desktop.Models.Anime"
    xmlns:asyncImageLoader="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ext="using:Dynamo_Desktop.Converters"
    xmlns:fluent="using:FluentAvalonia.UI.Controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:Dynamo_Desktop.ViewModels.Anime"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.DataContext>
        <vm:IndexViewModel SearchTerm="" />
    </Design.DataContext>
    <UserControl.Resources>
        <ext:EqualityConverter x:Key="EqualityConverter" />
        <ext:EqualityConverter x:Key="EmptyObject" />
    </UserControl.Resources>
    <Grid x:Name="MainGrid">
        <Grid IsVisible="{Binding SearchTerm, Converter={x:Static StringConverters.IsNullOrEmpty}}">
            <!--  For sort displays. Must not show if search term has a value or user is searching  -->
            <Grid>
                
                <ScrollViewer IsVisible="{Binding Sort, Converter={StaticResource EqualityConverter}, ConverterParameter='Newest'}" Margin="0,120,0,0">
                    <StackPanel>
                        <ItemsRepeater ItemsSource="{Binding RecentAnime}">
                            <ItemsRepeater.Layout>
                                <UniformGridLayout ItemsStretch="Fill" MaximumRowsOrColumns="4" />
                            </ItemsRepeater.Layout>
                            <ItemsRepeater.ItemTemplate>
                                <DataTemplate x:DataType="anime:PopularAnime">
                                    <Grid
                                        Classes="DataBox"
                                        Margin="10"
                                        Tapped="NavigateToDetails">
                                        <ContentControl Content="{Binding AnimeId}" IsVisible="False" />
                                        <ContentControl Content="{Binding Episode}" IsVisible="False" />
                                        <Grid Height="295" Margin="10">
                                            <asyncImageLoader:AdvancedImage Classes="DataImage" Source="{Binding Image}" />
                                            <Border
                                                Background="{DynamicResource SystemAccentColor}"
                                                CornerRadius="50"
                                                HorizontalAlignment="Right"
                                                Margin="35"
                                                Padding="15,5"
                                                VerticalAlignment="Bottom">
                                                <TextBlock Text="{Binding Episode}" />
                                            </Border>
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Text="{Binding Title}"
                                                TextTrimming="CharacterEllipsis"
                                                VerticalAlignment="Bottom" />
                                        </Grid>
                                    </Grid>
                                </DataTemplate>
                            </ItemsRepeater.ItemTemplate>
                        </ItemsRepeater>
                        <Button Content="Load More Anime" 
                                Command="{Binding LoadMore}" 
                                HorizontalAlignment="Center" 
                                Margin="20" 
                                Classes="accent" 
                                IsVisible="{Binding !DataLoading}" />
                    </StackPanel>
                </ScrollViewer>
                <!--  Popular Anime  -->
                <ScrollViewer IsVisible="{Binding Sort, Converter={StaticResource EqualityConverter}, ConverterParameter='Popular'}" Margin="0,120,0,0">
                    <StackPanel>
                        <ItemsRepeater ItemsSource="{Binding PopularAnime}">
                            <ItemsRepeater.Layout>
                                <UniformGridLayout ItemsStretch="Fill" MaximumRowsOrColumns="4" />
                            </ItemsRepeater.Layout>
                            <ItemsRepeater.ItemTemplate>
                                <DataTemplate x:DataType="anime:PopularAnime">
                                    <Grid
                                        Classes="DataBox"
                                        Margin="10"
                                        Tag="{Binding AnimeId}"
                                        Tapped="NavigateToDetails">
                                        <ContentControl Content="{Binding AnimeId}" IsVisible="False" />
                                        <ContentControl Content="{Binding Episode}" IsVisible="False" />
                                        <Grid Height="295" Margin="10">
                                            <asyncImageLoader:AdvancedImage Classes="DataImage" Source="{Binding Image}" />
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                Text="{Binding Title}"
                                                TextTrimming="CharacterEllipsis"
                                                VerticalAlignment="Bottom" />
                                        </Grid>
                                    </Grid>
                                </DataTemplate>
                            </ItemsRepeater.ItemTemplate>
                        </ItemsRepeater>
                        <Button Content="Load More Anime" 
                                Command="{Binding LoadMore}" 
                                HorizontalAlignment="Center" 
                                Margin="20" 
                                Classes="accent" 
                                IsVisible="{Binding !DataLoading}" />
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Grid>


        <!--  For search Displays  -->
        <Grid IsVisible="{Binding SearchTerm, Converter={x:Static StringConverters.IsNotNullOrEmpty}}">
            <TextBlock
                FontSize="20"
                HorizontalAlignment="Center"
                Margin="10">
                <TextBlock.Text>
                    <MultiBinding StringFormat="Search for &quot;{0}&quot;. {1} Result(s)">
                        <Binding Path="SearchTerm" />
                        <Binding FallbackValue="0 " Path="SearchResults.Count" />
                    </MultiBinding>
                </TextBlock.Text>
            </TextBlock>
            <ScrollViewer Margin="0,120,0,0">
                <ItemsRepeater ItemsSource="{Binding SearchResults}">
                    <ItemsRepeater.Layout>
                        <UniformGridLayout ItemsStretch="Fill" MaximumRowsOrColumns="4" />
                    </ItemsRepeater.Layout>
                    <ItemsRepeater.ItemTemplate>

                        <DataTemplate x:DataType="anime:PopularAnime">
                            <Grid
                                Classes="DataBox"
                                Margin="10"
                                Tag="{Binding AnimeId}"
                                Tapped="NavigateToDetails">
                                <ContentControl Content="{Binding AnimeId}" IsVisible="False" />
                                <ContentControl Content="{Binding Episode}" IsVisible="False" />
                                <Grid Height="295" Margin="10">
                                    <asyncImageLoader:AdvancedImage Classes="DataImage" Source="{Binding Image}" />
                                    <Border
                                        Background="{DynamicResource SystemAccentColor}"
                                        CornerRadius="50"
                                        HorizontalAlignment="Right"
                                        Margin="35"
                                        Padding="15,5"
                                        VerticalAlignment="Bottom">
                                        <TextBlock Text="{Binding subOrDub}" />
                                    </Border>
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        Text="{Binding Title}"
                                        TextTrimming="CharacterEllipsis"
                                        VerticalAlignment="Bottom" />
                                </Grid>
                            </Grid>
                        </DataTemplate>
                    </ItemsRepeater.ItemTemplate>
                </ItemsRepeater>
            </ScrollViewer>
            <TextBlock
                FontSize="20"
                HorizontalAlignment="Center"
                IsVisible="{Binding SearchResults.Count, Converter={StaticResource EqualityConverter}, ConverterParameter=0}"
                Text="{Binding SearchTerm, StringFormat='No data available for &quot;{0}&quot; search'}"
                VerticalAlignment="Center" />
        </Grid>
    </Grid>
</UserControl>
