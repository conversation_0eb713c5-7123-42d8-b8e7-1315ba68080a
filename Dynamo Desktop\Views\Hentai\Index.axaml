<UserControl
    x:Class="Dynamo_Desktop.Views.Hentai.Index"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:ext="using:Dynamo_Desktop.Converters"
    xmlns:fluent="using:FluentAvalonia.UI.Controls"
    xmlns:loaders="using:AsyncImageLoader.Loaders"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:subviews="using:Dynamo_Desktop.Views.Hentai.SubViews"
    xmlns:vm="using:Dynamo_Desktop.ViewModels.Hentai"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="vm:IndexViewModel"
    mc:Ignorable="d">
    <Grid>
        <ProgressBar
            Width="1000"
            VerticalAlignment="Top"
            IsIndeterminate="True"
            IsVisible="{Binding DataLoading}" />
        <!--  Controls  -->
        <Grid Margin="0,40,0,0" ZIndex="1">
            <!--  Sort Selection  -->
            <!--<fluent:ComboBox
                Margin="0,-20,20,0"
                HorizontalAlignment="Right"
                Header="Sort"
                Items="{Binding SortOptions}"
                SelectedItem="{Binding Sort}" />-->
            <!--  SORT TITLE  -->
            <TextBlock
                Margin="25"
                FontSize="35"
                Text="Recent" />
            <!--  Search  -->
            <Border
                Width="300"
				 
                Height="10"
                Margin="0,47,20,0"
                HorizontalAlignment="Right"
                VerticalAlignment="Top">
                <TextBox Text="{Binding SearchTerm}" Watermark="Search Providers" />
            </Border>
            <!--  Pagination  -->

            <StackPanel
                Margin="30,85,0,0"
                VerticalAlignment="Top"
                Orientation="Horizontal">
                <Button Command="{Binding PrevPage}" Content="Prev" />
                <Button Content="{Binding Page}" IsEnabled="False" />
                <Button Command="{Binding NextPage}" Content="Next" />
            </StackPanel>
        </Grid>

        <TabControl>
            <TabItem Header="Hanime">
                <subviews:IndexSubView Provider="Hanime" />
            </TabItem>

        </TabControl>

    </Grid>
</UserControl>
