<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AvaloniaProject">
    <option name="projectPerEditor">
      <map>
        <entry key="Dynamo Desktop/App.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Details.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Index.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Subviews/AnimePahe/DetailsSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Subviews/AnimePahe/IndexSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Subviews/DetailsSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Subviews/Gogo/DetailsSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Subviews/Gogo/IndexSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Subviews/IndexSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Subviews/KayoAnime/IndexSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Anime/Subviews/ZoroAnime/DetailsSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Hentai/Index.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Hentai/SubViews/DetailsSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Hentai/SubViews/IndexSubView.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/MainWindow.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/Settings.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
        <entry key="Dynamo Desktop/Views/SplashScreen/SplashScreen.axaml" value="Dynamo Desktop/Dynamo Desktop.csproj" />
      </map>
    </option>
  </component>
</project>