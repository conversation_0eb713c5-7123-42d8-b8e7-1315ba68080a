﻿using Dynamo_Desktop.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Dynamo_Desktop.Services;

public class SettingsService
{
    private static readonly string SettingsFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings.json");
    
    public static Settings GetSettings()
    {
        try
        {
            if (!File.Exists(SettingsFilePath))
            {
                // Create default settings if file doesn't exist
                var defaultSettings = CreateDefaultSettings();
                SetSettings(defaultSettings);
                return defaultSettings;
            }
            
            string jsonContent = File.ReadAllText(SettingsFilePath);
            return JsonSerializer.Deserialize<Settings>(jsonContent) ?? CreateDefaultSettings();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error reading settings: {ex.Message}");
            return CreateDefaultSettings();
        }
    }

    public static void SetSettings(Settings settings)
    {
        try
        {
            Debug.WriteLine("Attempting to save settings");
            string jsonContent = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(SettingsFilePath, jsonContent);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error saving settings: {ex.Message}");
        }
    }
    
    private static Settings CreateDefaultSettings()
    {
        return new Settings
        {
            Providers = new Providers
            {
                turkanime = new Provider { host = "https://www.turkanime.co" }
            },
            media_player = "mpv"
        };
    }
}
