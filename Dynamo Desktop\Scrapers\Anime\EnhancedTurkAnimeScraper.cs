using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Services;
using HtmlAgilityPack;
using Microsoft.Extensions.Caching.Memory;
using Polly;
using Polly.Extensions.Http;

namespace Dynamo_Desktop.Scrapers.Anime
{
    public class EnhancedTurkAnimeScraper : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly IMemoryCache _cache;
        private readonly SemaphoreSlim _rateLimitSemaphore;
        private readonly Random _random = new();
        
        private readonly string[] _userAgents = {
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        };

        private string Host => SettingsService.GetSettings().Providers.turkanime.host;

        public EnhancedTurkAnimeScraper()
        {
            _cache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = 100 // Limit cache size
            });
            
            _rateLimitSemaphore = new SemaphoreSlim(1, 1); // Allow 1 concurrent request
            
            var retryPolicy = HttpPolicyExtensions
                .HandleTransientHttpError()
                .OrResult(msg => !msg.IsSuccessStatusCode)
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(_random.Next(0, 1000)),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        Debug.WriteLine($"Retry {retryCount} for {context.OperationKey} in {timespan}ms");
                    });

            var handler = new HttpClientHandler()
            {
                UseCookies = true,
                AutomaticDecompression = DecompressionMethods.All
            };

            _httpClient = new HttpClient(handler);
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            
            // Set default headers
            _httpClient.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "tr-TR,tr;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate, br");
            _httpClient.DefaultRequestHeaders.Add("DNT", "1");
            _httpClient.DefaultRequestHeaders.Add("Connection", "keep-alive");
            _httpClient.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
        }

        public async Task<string> RecentAnime(int Page = 1)
        {
            var cacheKey = $"recent_anime_{Page}";
            
            if (_cache.TryGetValue(cacheKey, out string cachedResult))
            {
                Debug.WriteLine($"Cache hit for recent anime page {Page}");
                return cachedResult;
            }

            await _rateLimitSemaphore.WaitAsync();
            try
            {
                // Add random delay to appear more human-like
                await Task.Delay(_random.Next(1000, 3000));
                
                var recentAnime = new List<PopularAnime>();
                var url = $"{Host}/?sayfa={Page}";
                
                var response = await MakeRequestWithRetry(url);
                if (response != null)
                {
                    var htmlDoc = new HtmlDocument();
                    htmlDoc.LoadHtml(response);
                    
                    recentAnime = await ParseRecentAnime(htmlDoc);
                }
                
                var result = JsonSerializer.Serialize(recentAnime);
                
                // Cache for 5 minutes
                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(5));
                
                Debug.WriteLine($"Found {recentAnime.Count} recent anime on page {Page}");
                return result;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        public async Task<string> Search(string Query)
        {
            var cacheKey = $"search_{Query.ToLowerInvariant()}";
            
            if (_cache.TryGetValue(cacheKey, out string cachedResult))
            {
                Debug.WriteLine($"Cache hit for search: {Query}");
                return cachedResult;
            }

            await _rateLimitSemaphore.WaitAsync();
            try
            {
                await Task.Delay(_random.Next(1000, 3000));
                
                var searchResults = new List<PopularAnime>();
                var searchUrl = $"{Host}/arama?arama={Uri.EscapeDataString(Query)}";
                
                var response = await MakeRequestWithRetry(searchUrl);
                if (response != null)
                {
                    var htmlDoc = new HtmlDocument();
                    htmlDoc.LoadHtml(response);
                    
                    searchResults = await ParseSearchResults(htmlDoc);
                }
                
                var result = JsonSerializer.Serialize(searchResults);
                
                // Cache search results for 15 minutes
                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(15));
                
                Debug.WriteLine($"Found {searchResults.Count} search results for: {Query}");
                return result;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        private async Task<string> MakeRequestWithRetry(string url)
        {
            var policy = Policy
                .Handle<HttpRequestException>()
                .Or<TaskCanceledException>()
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                    onRetry: (exception, timeSpan, retryCount, context) =>
                    {
                        Debug.WriteLine($"Request retry {retryCount} for {url}: {exception.Message}");
                    });

            return await policy.ExecuteAsync(async () =>
            {
                using var request = new HttpRequestMessage(HttpMethod.Get, url);
                
                // Rotate user agent
                request.Headers.TryAddWithoutValidation("User-Agent", GetRandomUserAgent());
                request.Headers.TryAddWithoutValidation("Referer", Host);
                
                var response = await _httpClient.SendAsync(request);
                
                if (response.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    Debug.WriteLine("Rate limited, waiting longer...");
                    await Task.Delay(TimeSpan.FromSeconds(10));
                    throw new HttpRequestException("Rate limited");
                }
                
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            });
        }

        private string GetRandomUserAgent()
        {
            return _userAgents[_random.Next(_userAgents.Length)];
        }

        private Task<List<PopularAnime>> ParseRecentAnime(HtmlDocument htmlDoc)
        {
            var recentAnime = new List<PopularAnime>();
            var processedAnime = new HashSet<string>();

            // Multiple selector strategies for resilience
            var selectors = new[]
            {
                "//a[contains(@href, '/video/')]",
                "//a[contains(@href, '/anime/')]",
                "//div[contains(@class, 'episode')]//a",
                "//div[contains(@class, 'anime')]//a"
            };

            foreach (var selector in selectors)
            {
                var nodes = htmlDoc.DocumentNode.SelectNodes(selector);
                if (nodes != null)
                {
                    foreach (var node in nodes.Take(30))
                    {
                        try
                        {
                            var anime = ParseAnimeNode(node);
                            if (anime != null && processedAnime.Add(anime.AnimeId))
                            {
                                recentAnime.Add(anime);
                                if (recentAnime.Count >= 20) break;
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Error parsing anime node: {ex.Message}");
                        }
                    }
                    
                    if (recentAnime.Count >= 20) break;
                }
            }

            return Task.FromResult(recentAnime);
        }

        private Task<List<PopularAnime>> ParseSearchResults(HtmlDocument htmlDoc)
        {
            var searchResults = new List<PopularAnime>();
            
            var resultNodes = htmlDoc.DocumentNode.SelectNodes("//a[contains(@href, 'anime/')]");
            
            if (resultNodes != null)
            {
                foreach (var node in resultNodes.Take(20))
                {
                    try
                    {
                        var anime = ParseAnimeNode(node);
                        if (anime != null)
                        {
                            searchResults.Add(anime);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error parsing search result: {ex.Message}");
                    }
                }
            }

            return Task.FromResult(searchResults);
        }

        private PopularAnime ParseAnimeNode(HtmlNode node)
        {
            var href = node.GetAttributeValue("href", "");
            var title = node.GetAttributeValue("title", "") ??
                       node.InnerText?.Trim() ??
                       node.SelectSingleNode(".//text()")?.InnerText?.Trim();

            if (string.IsNullOrEmpty(href) || string.IsNullOrEmpty(title))
                return null;

            var animeId = ExtractAnimeIdFromUrl(href);
            if (string.IsNullOrEmpty(animeId))
                return null;

            // Try to get image from multiple sources
            var imageUrl = "";
            var imgSelectors = new[]
            {
                ".//img",
                "preceding-sibling::img[1]",
                "following-sibling::img[1]",
                "..//img",
                "../..//img"
            };

            foreach (var selector in imgSelectors)
            {
                var imgNode = node.SelectSingleNode(selector);
                if (imgNode != null)
                {
                    imageUrl = imgNode.GetAttributeValue("src", "") ??
                              imgNode.GetAttributeValue("data-src", "") ??
                              imgNode.GetAttributeValue("data-lazy", "");
                    if (!string.IsNullOrEmpty(imageUrl)) break;
                }
            }

            // Extract episode number if it's a video link
            int episodeNumber = 1;
            if (href.Contains("/video/"))
            {
                var episodeMatch = Regex.Match(href, @"-(\d+)(?:\?|$)");
                if (episodeMatch.Success)
                {
                    int.TryParse(episodeMatch.Groups[1].Value, out episodeNumber);
                }
            }

            return new PopularAnime
            {
                Title = CleanTitle(title),
                AnimeId = animeId,
                Image = !string.IsNullOrEmpty(imageUrl) ? MakeAbsoluteUrl(imageUrl) : "",
                Episode = episodeNumber
            };
        }

        public async Task<string> AnimeInfo(string Query)
        {
            var cacheKey = $"anime_info_{Query}";

            if (_cache.TryGetValue(cacheKey, out string cachedResult))
            {
                Debug.WriteLine($"Cache hit for anime info: {Query}");
                return cachedResult;
            }

            await _rateLimitSemaphore.WaitAsync();
            try
            {
                await Task.Delay(_random.Next(1000, 3000));

                var animeInfo = new AnimeInfo();
                var animeUrl = $"{Host}/anime/{Query}";

                var response = await MakeRequestWithRetry(animeUrl);
                if (response != null)
                {
                    var htmlDoc = new HtmlDocument();
                    htmlDoc.LoadHtml(response);

                    animeInfo = await ParseAnimeInfo(htmlDoc, Query, response);
                }

                var result = JsonSerializer.Serialize(animeInfo);

                // Cache anime info for 30 minutes
                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(30));

                Debug.WriteLine($"Extracted info for {animeInfo.Title} with {animeInfo.Episodes?.Count ?? 0} episodes");
                return result;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        public async Task<string> EpisodeStreamLinks(string AnimeId, int Episode)
        {
            var cacheKey = $"stream_links_{AnimeId}_{Episode}";

            if (_cache.TryGetValue(cacheKey, out string cachedResult))
            {
                Debug.WriteLine($"Cache hit for stream links: {AnimeId} Episode {Episode}");
                return cachedResult;
            }

            await _rateLimitSemaphore.WaitAsync();
            try
            {
                await Task.Delay(_random.Next(1000, 3000));

                var streamingLinks = new List<AnimeStreamingLinks>();
                var episodeUrl = $"{Host}/video/{AnimeId}-{Episode}";

                var response = await MakeRequestWithRetry(episodeUrl);
                if (response != null)
                {
                    var htmlDoc = new HtmlDocument();
                    htmlDoc.LoadHtml(response);

                    streamingLinks = await ParseStreamingLinks(htmlDoc, response);
                }

                var result = JsonSerializer.Serialize(streamingLinks);

                // Cache streaming links for 10 minutes
                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10));

                Debug.WriteLine($"Found {streamingLinks.Count} streaming links for {AnimeId} Episode {Episode}");
                return result;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        private Task<AnimeInfo> ParseAnimeInfo(HtmlDocument htmlDoc, string query, string responseBody)
        {
            var animeInfo = new AnimeInfo();

            // Extract title with multiple fallback strategies
            var titleSelectors = new[] { "//h1", "//title", "//h2", "//h3" };
            foreach (var selector in titleSelectors)
            {
                var titleNode = htmlDoc.DocumentNode.SelectSingleNode(selector);
                if (titleNode != null)
                {
                    animeInfo.Title = titleNode.InnerText?.Trim()
                        .Replace(" izle", "")
                        .Replace("-->", "")
                        .Trim();
                    if (!string.IsNullOrEmpty(animeInfo.Title)) break;
                }
            }

            if (string.IsNullOrEmpty(animeInfo.Title))
                animeInfo.Title = query;

            // Extract description with multiple strategies
            var descriptionText = "";

            // Try JSON-LD first
            var jsonLdDescMatch = Regex.Match(responseBody, @"""description"":\s*""([^""]+)""");
            if (jsonLdDescMatch.Success)
            {
                descriptionText = jsonLdDescMatch.Groups[1].Value
                    .Replace("\\\"", "\"")
                    .Replace("\\n", "\n")
                    .Replace("\\r", "");
            }
            else
            {
                // Try HTML parsing
                var descSelectors = new[]
                {
                    "//div[contains(@class, 'description')]",
                    "//div[contains(@class, 'summary')]",
                    "//div[contains(@class, 'plot')]",
                    "//p[contains(text(), 'Özet')]"
                };

                foreach (var selector in descSelectors)
                {
                    var descNode = htmlDoc.DocumentNode.SelectSingleNode(selector);
                    if (descNode != null)
                    {
                        descriptionText = descNode.InnerText?.Trim();
                        if (!string.IsNullOrEmpty(descriptionText)) break;
                    }
                }

                // Fallback to regex
                if (string.IsNullOrEmpty(descriptionText))
                {
                    var ozetMatch = Regex.Match(responseBody, @"Özet;\s*([^{]+?)(?=\{|$)", RegexOptions.Singleline);
                    if (ozetMatch.Success)
                    {
                        descriptionText = ozetMatch.Groups[1].Value.Trim();
                        var endMarkers = new[] { "Disqus Yorumlar", "FANSUB DUYURU", "BEĞENİLEN BÖLÜMLER" };
                        foreach (var marker in endMarkers)
                        {
                            var endIndex = descriptionText.IndexOf(marker);
                            if (endIndex > 0)
                            {
                                descriptionText = descriptionText.Substring(0, endIndex).Trim();
                                break;
                            }
                        }
                    }
                }
            }

            animeInfo.Description = descriptionText;

            // Extract image with multiple strategies
            var jsonLdMatch = Regex.Match(responseBody, @"""image"":\s*""([^""]+)""");
            if (jsonLdMatch.Success)
            {
                animeInfo.Image = jsonLdMatch.Groups[1].Value;
            }
            else
            {
                var imgSelectors = new[]
                {
                    "//img[contains(@src, 'imajlar')]",
                    "//img[contains(@class, 'poster')]",
                    "//img[contains(@class, 'cover')]",
                    "//div[contains(@class, 'poster')]//img",
                    "//meta[@property='og:image']"
                };

                foreach (var selector in imgSelectors)
                {
                    var imgNode = htmlDoc.DocumentNode.SelectSingleNode(selector);
                    if (imgNode != null)
                    {
                        var imgSrc = imgNode.GetAttributeValue("src", "") ??
                                    imgNode.GetAttributeValue("content", "");
                        if (!string.IsNullOrEmpty(imgSrc))
                        {
                            animeInfo.Image = MakeAbsoluteUrl(imgSrc);
                            break;
                        }
                    }
                }
            }

            // Extract episode count and create episodes list
            animeInfo.Episodes = new List<AnimeEpisodes>();
            int totalEpisodes = 1;

            // Try multiple strategies to find episode count
            var episodeCountPatterns = new[]
            {
                @"Bölüm Sayısı[^>]*>.*?</td>.*?</td>.*?>(\d+)(?:\s*/\s*\d+)?",
                @"Episode Count[^>]*>.*?>(\d+)",
                @"Episodes[^>]*>.*?>(\d+)",
                @"(\d+)\s*(?:Bölüm|Episode)"
            };

            foreach (var pattern in episodeCountPatterns)
            {
                var match = Regex.Match(responseBody, pattern, RegexOptions.Singleline | RegexOptions.IgnoreCase);
                if (match.Success && int.TryParse(match.Groups[1].Value, out int episodeCount))
                {
                    totalEpisodes = episodeCount;
                    break;
                }
            }

            // Fallback: count video links
            if (totalEpisodes == 1)
            {
                var videoLinkMatches = Regex.Matches(responseBody, $@"/video/{Regex.Escape(query)}-(\d+)");
                if (videoLinkMatches.Count > 0)
                {
                    var maxEpisode = videoLinkMatches
                        .Cast<Match>()
                        .Select(m => int.TryParse(m.Groups[1].Value, out int ep) ? ep : 0)
                        .Max();
                    if (maxEpisode > 0)
                    {
                        totalEpisodes = maxEpisode;
                    }
                }
            }

            // Create episodes list
            for (int i = 1; i <= totalEpisodes; i++)
            {
                animeInfo.Episodes.Add(new AnimeEpisodes
                {
                    EpisodeNumber = i,
                    EpisodeId = $"{query}-{i}"
                });
            }

            animeInfo.EpisodeCount = totalEpisodes;
            return Task.FromResult(animeInfo);
        }

        private Task<List<AnimeStreamingLinks>> ParseStreamingLinks(HtmlDocument htmlDoc, string responseBody)
        {
            var streamingLinks = new List<AnimeStreamingLinks>();

            // Strategy 1: Look for iframe sources
            var iframeNodes = htmlDoc.DocumentNode.SelectNodes("//iframe[@src]");
            if (iframeNodes != null)
            {
                foreach (var iframe in iframeNodes)
                {
                    var src = iframe.GetAttributeValue("src", "");
                    if (!string.IsNullOrEmpty(src) && IsValidVideoSource(src))
                    {
                        streamingLinks.Add(new AnimeStreamingLinks
                        {
                            Quality = "Default",
                            Source = MakeAbsoluteUrl(src)
                        });
                    }
                }
            }

            // Strategy 2: Look for video tags
            var videoSelectors = new[]
            {
                "//video[@src]",
                "//video//source[@src]",
                "//source[@src]"
            };

            foreach (var selector in videoSelectors)
            {
                var videoNodes = htmlDoc.DocumentNode.SelectNodes(selector);
                if (videoNodes != null)
                {
                    foreach (var video in videoNodes)
                    {
                        var src = video.GetAttributeValue("src", "");
                        if (!string.IsNullOrEmpty(src) && IsValidVideoSource(src))
                        {
                            streamingLinks.Add(new AnimeStreamingLinks
                            {
                                Quality = "HD",
                                Source = MakeAbsoluteUrl(src)
                            });
                        }
                    }
                }
            }

            // Strategy 3: Look for JavaScript embedded video URLs
            var scriptNodes = htmlDoc.DocumentNode.SelectNodes("//script[contains(text(), 'http')]");
            if (scriptNodes != null)
            {
                foreach (var script in scriptNodes)
                {
                    var scriptContent = script.InnerText;

                    // Multiple URL patterns for different video formats
                    var urlPatterns = new[]
                    {
                        @"['""]([^'""]*\.(?:mp4|m3u8|mkv|avi|webm|flv)[^'""]*)['""]",
                        @"['""]([^'""]*(?:video|stream|play)[^'""]*)['""]",
                        @"src['""\s]*:['""\s]*['""]([^'""]+)['""]",
                        @"file['""\s]*:['""\s]*['""]([^'""]+)['""]"
                    };

                    foreach (var pattern in urlPatterns)
                    {
                        var urlMatches = Regex.Matches(scriptContent, pattern);
                        foreach (Match match in urlMatches)
                        {
                            var url = match.Groups[1].Value;
                            if (IsValidVideoSource(url))
                            {
                                streamingLinks.Add(new AnimeStreamingLinks
                                {
                                    Quality = url.Contains("m3u8") ? "HLS" :
                                             url.Contains("1080") ? "1080p" :
                                             url.Contains("720") ? "720p" : "MP4",
                                    Source = MakeAbsoluteUrl(url)
                                });
                            }
                        }
                    }
                }
            }

            // Strategy 4: Look for data attributes
            var dataNodes = htmlDoc.DocumentNode.SelectNodes("//*[@data-src or @data-video or @data-stream]");
            if (dataNodes != null)
            {
                foreach (var node in dataNodes)
                {
                    var dataSrc = node.GetAttributeValue("data-src", "") ??
                                 node.GetAttributeValue("data-video", "") ??
                                 node.GetAttributeValue("data-stream", "");

                    if (!string.IsNullOrEmpty(dataSrc) && IsValidVideoSource(dataSrc))
                    {
                        streamingLinks.Add(new AnimeStreamingLinks
                        {
                            Quality = "Data",
                            Source = MakeAbsoluteUrl(dataSrc)
                        });
                    }
                }
            }

            // Remove duplicates and invalid links
            streamingLinks = streamingLinks
                .Where(s => !string.IsNullOrEmpty(s.Source))
                .GroupBy(s => s.Source)
                .Select(g => g.First())
                .ToList();

            return Task.FromResult(streamingLinks);
        }

        private string ExtractAnimeIdFromUrl(string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url)) return "";

                // Handle relative URLs
                if (url.StartsWith("/"))
                {
                    url = Host + url;
                }

                // Multiple patterns for different URL structures
                var patterns = new[]
                {
                    @"/anime/([^/\?]+)",
                    @"/video/([^/\?]+)",
                    @"anime[=/]([^/\?&]+)",
                    @"video[=/]([^/\?&]+)"
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(url, pattern);
                    if (match.Success)
                    {
                        var fullId = match.Groups[1].Value;

                        // Clean up episode numbers and suffixes
                        var cleanPatterns = new[]
                        {
                            @"-\d+-bolum$",
                            @"-\d+$",
                            @"-bolum-\d+$",
                            @"-episode-\d+$"
                        };

                        foreach (var cleanPattern in cleanPatterns)
                        {
                            var cleanedId = Regex.Replace(fullId, cleanPattern, "");
                            if (cleanedId != fullId && !string.IsNullOrEmpty(cleanedId))
                            {
                                return cleanedId;
                            }
                        }

                        return fullId;
                    }
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        private string CleanTitle(string title)
        {
            if (string.IsNullOrEmpty(title)) return "";

            title = title.Trim();

            // Remove common patterns
            var cleanPatterns = new[]
            {
                @"\s+", // Multiple spaces to single space
                @"\d+\.\s*Bölüm.*$", // Remove episode info
                @"izle.*$", // Remove "izle" suffix
                @"Episode\s*\d+.*$", // Remove episode numbers
                @"Ep\s*\d+.*$", // Remove ep numbers
                @"\s*-\s*$", // Remove trailing dashes
                @"^\s*-\s*" // Remove leading dashes
            };

            var replacements = new[] { " ", "", "", "", "", "", "" };

            for (int i = 0; i < cleanPatterns.Length; i++)
            {
                title = Regex.Replace(title, cleanPatterns[i], replacements[i], RegexOptions.IgnoreCase);
            }

            return title.Trim();
        }

        private string MakeAbsoluteUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return "";

            if (url.StartsWith("http"))
            {
                return url;
            }
            else if (url.StartsWith("//"))
            {
                return "https:" + url;
            }
            else if (url.StartsWith("/"))
            {
                return Host + url;
            }
            else
            {
                return Host + "/" + url;
            }
        }

        private bool IsValidVideoSource(string url)
        {
            if (string.IsNullOrEmpty(url)) return false;

            var lowerUrl = url.ToLower();

            // Check for video file extensions
            var videoExtensions = new[] { ".mp4", ".m3u8", ".mkv", ".avi", ".webm", ".flv", ".mov", ".wmv" };
            if (videoExtensions.Any(ext => lowerUrl.Contains(ext)))
                return true;

            // Check for streaming keywords
            var streamingKeywords = new[] { "video", "stream", "play", "watch", "embed" };
            if (streamingKeywords.Any(keyword => lowerUrl.Contains(keyword)))
                return true;

            // Check for known streaming domains
            var streamingDomains = new[] { "youtube", "vimeo", "dailymotion", "streamtape", "doodstream" };
            if (streamingDomains.Any(domain => lowerUrl.Contains(domain)))
                return true;

            return false;
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
            _cache?.Dispose();
            _rateLimitSemaphore?.Dispose();
        }
    }
}
