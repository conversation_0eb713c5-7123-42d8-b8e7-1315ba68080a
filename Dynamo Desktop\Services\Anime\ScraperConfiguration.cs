using System;

namespace Dynamo_Desktop.Services.Anime
{
    public class ScraperConfiguration
    {
        /// <summary>
        /// Maximum number of retry attempts for failed requests
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Base delay between requests in milliseconds (randomized)
        /// </summary>
        public int BaseRequestDelayMs { get; set; } = 1000;

        /// <summary>
        /// Maximum delay between requests in milliseconds
        /// </summary>
        public int MaxRequestDelayMs { get; set; } = 3000;

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public int RequestTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Cache duration for recent anime results in minutes
        /// </summary>
        public int RecentAnimeCacheMinutes { get; set; } = 5;

        /// <summary>
        /// Cache duration for search results in minutes
        /// </summary>
        public int SearchCacheMinutes { get; set; } = 15;

        /// <summary>
        /// Cache duration for anime info in minutes
        /// </summary>
        public int AnimeInfoCacheMinutes { get; set; } = 30;

        /// <summary>
        /// Cache duration for streaming links in minutes
        /// </summary>
        public int StreamingLinksCacheMinutes { get; set; } = 10;

        /// <summary>
        /// Maximum number of concurrent requests
        /// </summary>
        public int MaxConcurrentRequests { get; set; } = 1;

        /// <summary>
        /// Maximum cache size (number of items)
        /// </summary>
        public int MaxCacheSize { get; set; } = 100;

        /// <summary>
        /// Whether to use browser automation for JavaScript-heavy pages
        /// </summary>
        public bool UseBrowserAutomation { get; set; } = false;

        /// <summary>
        /// Whether to enable detailed logging
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = true;

        /// <summary>
        /// Whether to respect robots.txt
        /// </summary>
        public bool RespectRobotsTxt { get; set; } = true;

        /// <summary>
        /// Custom user agent string (if empty, will use rotating user agents)
        /// </summary>
        public string CustomUserAgent { get; set; } = "";

        /// <summary>
        /// Proxy settings (if needed)
        /// </summary>
        public ProxySettings Proxy { get; set; } = new ProxySettings();

        /// <summary>
        /// CloudFlare bypass settings
        /// </summary>
        public CloudFlareSettings CloudFlare { get; set; } = new CloudFlareSettings();

        public static ScraperConfiguration Default => new ScraperConfiguration();

        public static ScraperConfiguration Conservative => new ScraperConfiguration
        {
            MaxRetryAttempts = 5,
            BaseRequestDelayMs = 2000,
            MaxRequestDelayMs = 5000,
            RecentAnimeCacheMinutes = 10,
            SearchCacheMinutes = 30,
            AnimeInfoCacheMinutes = 60,
            StreamingLinksCacheMinutes = 20,
            RespectRobotsTxt = true
        };

        public static ScraperConfiguration Fast => new ScraperConfiguration
        {
            MaxRetryAttempts = 2,
            BaseRequestDelayMs = 500,
            MaxRequestDelayMs = 1500,
            RecentAnimeCacheMinutes = 2,
            SearchCacheMinutes = 5,
            AnimeInfoCacheMinutes = 10,
            StreamingLinksCacheMinutes = 5,
            MaxConcurrentRequests = 2
        };
    }

    public class ProxySettings
    {
        public bool Enabled { get; set; } = false;
        public string Host { get; set; } = "";
        public int Port { get; set; } = 8080;
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
        public ProxyType Type { get; set; } = ProxyType.Http;
    }

    public enum ProxyType
    {
        Http,
        Socks4,
        Socks5
    }

    public class CloudFlareSettings
    {
        public bool BypassEnabled { get; set; } = false;
        public int MaxWaitTimeSeconds { get; set; } = 30;
        public bool UseHeadlessBrowser { get; set; } = true;
    }
}
