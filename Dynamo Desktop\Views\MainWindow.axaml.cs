using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Dynamo_Desktop.Scrapers.Anime;
using Dynamo_Desktop.Services;
using Dynamo_Desktop.ViewModels;
using Dynamo_Desktop.Views;
using Dynamo_Desktop.Views.Hentai;
using FluentAvalonia.Core;
using FluentAvalonia.UI.Controls;
using FluentAvalonia.UI.Navigation;
using FluentAvalonia.UI.Windowing;
using System;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Media;
using Dynamo_Desktop.Views.SplashScreen;

namespace Dynamo_Desktop.Views
{
    public enum NavigationPage
    {
        Anime,
        Hentai,
        Settings
    }
    public partial class MainWindow : AppWindow
    {
        public new bool IsWindows = true;
        public MainWindow()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                Background = Brushes.Transparent;
            }
            
            InitializeComponent();
            ContentFrame.Navigate(typeof(Anime.Index));
            
            nvSample.AttachedToVisualTree += (sender, args) =>
            {
                if (nvSample.MenuItems?.Any() == true)
                {
                    nvSample.SelectedItem = nvSample.MenuItems.ElementAt(0);
                }
            };
            
            TitleBar.TitleBarHitTestType = TitleBarHitTestType.Complex;
            TitleBar.ExtendsContentIntoTitleBar = true;
            TitleBar.ButtonHoverBackgroundColor = Color.FromArgb(50, 255, 255, 255);
            TitleBar.ButtonInactiveForegroundColor = Color.Parse("white");

            nvSample.PropertyChanged += (sender, args) =>
            {
                if (args.Property?.Name == "SelectedItem" && args.NewValue != null)
                {
                    NavigateToPage(args.NewValue.ToString());
                }
            };
        }
        
        private void NavigateToPage(string pageName)
        {
            try
            {
                switch (pageName)
                {
                    case "Hentai":
                        ContentFrame.Navigate(typeof(Hentai.Index));
                        break;
                    case "Anime":
                        ContentFrame.Navigate(typeof(Anime.Index));
                        break;
                    case "Settings":
                        ContentFrame.Navigate(typeof(Settings));
                        break;
                    default:
                        Debug.WriteLine($"Unknown navigation target: {pageName}");
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Navigation error: {ex.Message}");
            }
        }
        public new void BackRequested(object sender, NavigationViewBackRequestedEventArgs e)
        {
            if (ContentFrame.CanGoBack)
            {
                ContentFrame.GoBack();
            }
        }
        public void Frame_Navigating(object sender, NavigatingCancelEventArgs e)
        {
            ContentFrame.Tag = e.Parameter;
        }
        public void NavigationItemChanged(object sender, AvaloniaPropertyChangedEventArgs e)
        {
          
        }
     
    }

    public class ApplicationSplashScreen : IApplicationSplashScreen
    {
        public Task RunTasks(CancellationToken cancellationToken)
        {
            return Task.Run(async () =>
            {
                try
                {
                    // Initialize application services
                    await Task.Delay(1000, cancellationToken);
                    
                    // Load settings
                    _ = SettingsService.GetSettings();
                    
                    await Task.Delay(500, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Splash screen initialization error: {ex.Message}");
                }
            }, cancellationToken);
        }
        
        public string AppName { get; } = "Dynamo Desktop";
        public IImage AppIcon { get; } = null; // Could load from Assets if needed
        public object SplashScreenContent => new Views.SplashScreen.SplashScreen();
        public int MinimumShowTime => 2000; // Reduced from 10 seconds to 2 seconds
    }
}
