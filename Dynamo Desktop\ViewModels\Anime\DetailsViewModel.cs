﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text.Json;
using System.Threading.Tasks;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Services.Anime;
using FluentAvalonia.UI.Controls;
using ReactiveUI;
using ReactiveUI.Fody.Helpers;


namespace Dynamo_Desktop.ViewModels.Anime
{
    public class DetailsViewModel : ReactiveObject
    {
        private AnimeIndexToDetailsRouteParams _routeParams;

        public AnimeIndexToDetailsRouteParams RouteParams
        {
            get => _routeParams;
            set
            {
                this.RaiseAndSetIfChanged(ref _routeParams, value);
                GetInfo();
            }
        }

        [Reactive]
        public AnimeInfo Info { get; set; } = new AnimeInfo();
        
        [Reactive]
        public List<AnimeStreamingLinks> Links { get; set; } = new List<AnimeStreamingLinks>();
        
        [Reactive]
        public bool DataLoading { get; set; } = false;
        private IAnimeService AnimeService { get; set; }
        
        public DetailsViewModel()
        {
            Info = new AnimeInfo { Episodes = new List<AnimeEpisodes>() };
            Links = new List<AnimeStreamingLinks>();
        }
        public async void GetInfo()
        {
            if (RouteParams == null)
            {
                Debug.WriteLine("RouteParams is null");
                return;
            }
            
            DataLoading = true;
            
            try
            {
                switch (RouteParams.Provider)
                {
                    case AnimeProviders.TurkAnime:
                        AnimeService = new TurkAnimeService();
                        break;
                    default:
                        AnimeService = new TurkAnimeService();
                        break;
                }
                
                Info = await AnimeService.Info(RouteParams.AnimeId);
                if (Info == null)
                {
                    var dialog = new ContentDialog()
                    {  
                        Content = "Could not obtain anime information. Please check your internet connection or try another provider.",
                        Title = "Error",
                        DefaultButton = ContentDialogButton.Close,
                        CloseButtonText = "OK"
                    };
                    await dialog.ShowAsync();
                    DataLoading = false;
                    return;
                }
                
                Debug.WriteLine($"Fetching streaming links for {RouteParams.AnimeId} episode {RouteParams.EpisodeNumber}");
                Links = await AnimeService.StreamingLinks(Query: RouteParams.AnimeId, Episode: RouteParams.EpisodeNumber);
                
                if (Links == null || Links.Count == 0)
                {
                    Debug.WriteLine($"No streaming links found for {RouteParams.AnimeId} episode {RouteParams.EpisodeNumber}");

                    var dialog = new ContentDialog()
                    {
                        Content = $"No streaming links available for episode {RouteParams.EpisodeNumber}. This episode might not be available yet or the provider is having issues.",
                        Title = "No Streams Available",
                        DefaultButton = ContentDialogButton.Close,
                        CloseButtonText = "OK"
                    };
                    await dialog.ShowAsync();
                }
                else
                {
                    Debug.WriteLine($"Found {Links.Count} streaming links");
                    foreach (var link in Links)
                    {
                        Debug.WriteLine($"Quality: {link.Quality}, Source: {link.Source}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting anime info: {ex.Message}");
                var dialog = new ContentDialog()
                {
                    Content = $"An error occurred: {ex.Message}",
                    Title = "Error",
                    DefaultButton = ContentDialogButton.Close,
                    CloseButtonText = "OK"
                };
                await dialog.ShowAsync();
            }
            finally
            {
                DataLoading = false;
            }
        }

    }
}
