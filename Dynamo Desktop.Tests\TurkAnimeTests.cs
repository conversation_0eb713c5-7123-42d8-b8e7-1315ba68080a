using System.Diagnostics;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Scrapers.Anime;
using Dynamo_Desktop.Services.Anime;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Dynamo_Desktop.Tests;

[TestClass]
public class TurkAnimeTests
{
    private TurkAnimeScraper _scraper = new();
    
    [TestMethod]
    public async Task TestRecentAnime()
    {
        // Test recent anime fetching
        var response = JsonSerializer.Deserialize<List<PopularAnime>>(
            await _scraper.RecentAnime(Page: 1));
        Assert.IsNotNull(response);
        Assert.IsInstanceOfType<List<PopularAnime>>(response);
        
        Debug.WriteLine($"Found {response.Count} recent anime");
        foreach (var anime in response.Take(3))
        {
            Debug.WriteLine($"Anime: {anime.Title} (ID: {anime.AnimeId})");
        }
    }
    
    [TestMethod]
    public async Task TestSearch()
    {
        foreach (var searchTerm in Commons.SearchTerms.Take(2)) // Limit to 2 search terms
        {
            var response = JsonSerializer.Deserialize<List<PopularAnime>>(
                await _scraper.Search(Query: searchTerm));
            Assert.IsNotNull(response);
            Assert.IsInstanceOfType<List<PopularAnime>>(response);
            
            Debug.WriteLine($"Search '{searchTerm}' found {response.Count} results");
        }
    }
    
    [TestMethod]
    public async Task TestAnimeInfo()
    {
        var recents = JsonSerializer.Deserialize<List<PopularAnime>>(
            await _scraper.RecentAnime(Page: 1));
        Assert.IsNotNull(recents);
        
        if (recents.Count > 0)
        {
            var testAnime = recents[0];
            var info = JsonSerializer.Deserialize<AnimeInfo>(
                await _scraper.AnimeInfo(Query: testAnime.AnimeId));
            Assert.IsNotNull(info);
            Assert.IsInstanceOfType<AnimeInfo>(info);
            
            Debug.WriteLine($"Anime Info: {info.Title}");
            Debug.WriteLine($"Episodes: {info.Episodes?.Count ?? 0}");
        }
    }
    
    [TestMethod]
    public async Task TestStreamingLinks()
    {
        var recents = JsonSerializer.Deserialize<List<PopularAnime>>(
            await _scraper.RecentAnime(Page: 1));
        Assert.IsNotNull(recents);
        
        if (recents.Count > 0)
        {
            var testAnime = recents[0];
            var info = JsonSerializer.Deserialize<AnimeInfo>(
                await _scraper.AnimeInfo(Query: testAnime.AnimeId));
            Assert.IsNotNull(info);
            
            if (info.Episodes != null && info.Episodes.Count > 0)
            {
                var streamingLinks = JsonSerializer.Deserialize<List<AnimeStreamingLinks>>(
                    await _scraper.EpisodeStreamLinks(AnimeId: testAnime.AnimeId, Episode: 1));
                Assert.IsNotNull(streamingLinks);
                Assert.IsInstanceOfType<List<AnimeStreamingLinks>>(streamingLinks);
                
                Debug.WriteLine($"Found {streamingLinks.Count} streaming links for {testAnime.Title}");
                foreach (var link in streamingLinks)
                {
                    Debug.WriteLine($"Quality: {link.Quality}, Source: {link.Source}");
                }
            }
        }
    }
    
    [TestMethod]
    public async Task TestTurkAnimeService()
    {
        var service = new TurkAnimeService();
        
        // Test recent anime
        var recentAnime = await service.RecentAnime(1);
        Assert.IsNotNull(recentAnime);
        Assert.IsInstanceOfType<List<PopularAnime>>(recentAnime);
        
        if (recentAnime.Count > 0)
        {
            var testAnime = recentAnime[0];
            Debug.WriteLine($"Testing service with: {testAnime.Title}");
            
            // Test anime info
            var animeInfo = await service.Info(testAnime.AnimeId);
            Assert.IsNotNull(animeInfo);
            Assert.IsInstanceOfType<AnimeInfo>(animeInfo);
            
            // Test streaming links
            var streamingLinks = await service.StreamingLinks(testAnime.AnimeId, 1);
            Assert.IsNotNull(streamingLinks);
            Assert.IsInstanceOfType<List<AnimeStreamingLinks>>(streamingLinks);
            
            Debug.WriteLine($"Service test completed successfully");
        }
    }
}
