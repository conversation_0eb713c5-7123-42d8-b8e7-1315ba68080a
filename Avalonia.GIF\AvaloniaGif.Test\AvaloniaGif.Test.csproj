<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Inputs\*.gif" />
    <EmbeddedResource Include="Inputs\*.gif" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="nunit" Version="3.12.0" />
    <PackageReference Include="ApprovalTests" Version="5.2.4" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.5.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="3.15.1" />
    <PackageReference Include="coverlet.collector" Version="3.1.2" />

    <PackageReference Include="Avalonia" Version="0.10.13" />
    <PackageReference Include="Avalonia.Desktop" Version="0.10.13" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AvaloniaGif\AvaloniaGif.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Apps\App.axaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
  </ItemGroup>
</Project>
