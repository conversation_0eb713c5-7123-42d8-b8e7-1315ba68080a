﻿<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:gif="clr-namespace:AvaloniaGif;assembly=AvaloniaGif"
        xmlns:demo="clr-namespace:AvaloniaGif.Demo"
        x:Class="AvaloniaGif.Demo.MainWindow"
        Title="GIFs on Avalonia!"
        Height="480" Width="680"
        x:CompileBindings="True"
        x:DataType="demo:MainWindowViewModel"
        Background="White">
    <Grid ColumnDefinitions="2*, 3*">
        <StackPanel Grid.Column="0">
            <ComboBox SelectedItem="{Binding Path=Stretch}"
                      Items="{Binding Path=Stretches}" />
            <ListBox SelectedItem="{Binding Path=SelectedGif}"
                     Items="{Binding Path=AvailableGifs}" />
        </StackPanel>
        <gif:GifImage Grid.Column="1" SourceUriRaw="{Binding Path=SelectedGif}" 
                      Stretch="{Binding Path=Stretch}"
                      StretchDirection="Both" />
    </Grid>
</Window>