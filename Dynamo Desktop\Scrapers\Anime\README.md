# Enhanced TurkAnime Scraper

## Overview

The Enhanced TurkAnime Scraper is a robust, production-ready solution for fetching anime episodes from Turkanime.co. It addresses all the stability issues present in the original scraper and provides a much more reliable experience.

## Key Improvements

### 🛡️ **Stability Features**
- **Retry Logic**: Exponential backoff with jitter for failed requests
- **Rate Limiting**: Prevents IP bans by controlling request frequency
- **User Agent Rotation**: Multiple browser user agents to avoid detection
- **Request Throttling**: Random delays between requests to appear human-like
- **Error Handling**: Comprehensive exception handling with graceful degradation

### ⚡ **Performance Features**
- **Memory Caching**: Intelligent caching with configurable TTL
- **Connection Reuse**: Single HttpClient instance with proper configuration
- **Concurrent Request Control**: Semaphore-based request limiting
- **Response Compression**: Automatic gzip/deflate support

### 🔍 **Parsing Resilience**
- **Multiple Selector Strategies**: Fallback selectors for HTML parsing
- **Flexible URL Extraction**: Multiple regex patterns for different URL formats
- **Content Validation**: Validates extracted data before processing
- **Image Source Detection**: Multiple strategies for finding anime images

## Usage

### Basic Usage

```csharp
// Create the enhanced scraper
using var scraper = new EnhancedTurkAnimeScraper();

// Get recent anime
var recentJson = await scraper.RecentAnime(page: 1);
var recentAnime = JsonSerializer.Deserialize<List<PopularAnime>>(recentJson);

// Search for anime
var searchJson = await scraper.Search("naruto");
var searchResults = JsonSerializer.Deserialize<List<PopularAnime>>(searchJson);

// Get anime information
var infoJson = await scraper.AnimeInfo("anime-id");
var animeInfo = JsonSerializer.Deserialize<AnimeInfo>(infoJson);

// Get streaming links
var linksJson = await scraper.EpisodeStreamLinks("anime-id", episode: 1);
var streamingLinks = JsonSerializer.Deserialize<List<AnimeStreamingLinks>>(linksJson);
```

### Service Layer Usage

```csharp
// Use the enhanced service (recommended)
using var service = new EnhancedTurkAnimeService();

var recentAnime = await service.RecentAnime(page: 1);
var searchResults = await service.Search("one piece");
var animeInfo = await service.Info("anime-id");
var streamingLinks = await service.StreamingLinks("anime-id", episode: 1);
```

## Configuration

The scraper uses intelligent defaults but can be customized:

```csharp
// Use predefined configurations
var config = ScraperConfiguration.Conservative; // More delays, longer cache
var config = ScraperConfiguration.Fast;        // Faster but less stable
var config = ScraperConfiguration.Default;     // Balanced approach
```

## Caching Strategy

The enhanced scraper implements intelligent caching:

- **Recent Anime**: 5 minutes (frequently changing)
- **Search Results**: 15 minutes (moderately stable)
- **Anime Info**: 30 minutes (rarely changes)
- **Streaming Links**: 10 minutes (may change due to server issues)

## Error Handling

The scraper handles various error scenarios:

- **Network Issues**: Automatic retry with exponential backoff
- **Rate Limiting**: Detects 429 responses and waits appropriately
- **Invalid Data**: Returns empty results instead of throwing exceptions
- **Site Changes**: Multiple parsing strategies provide resilience

## Monitoring and Debugging

Enable detailed logging to monitor scraper behavior:

```csharp
// Logs are written to Debug output
Debug.WriteLine("Scraper activity and errors");
```

Key metrics to monitor:
- Request success rate
- Cache hit ratio
- Average response time
- Error frequency

## Best Practices

### 1. **Resource Management**
```csharp
// Always dispose of scrapers and services
using var scraper = new EnhancedTurkAnimeScraper();
using var service = new EnhancedTurkAnimeService();
```

### 2. **Error Handling**
```csharp
try
{
    var results = await service.RecentAnime();
    if (results.Count == 0)
    {
        // Handle empty results gracefully
        ShowNoResultsMessage();
    }
}
catch (Exception ex)
{
    // Log error and show user-friendly message
    LogError(ex);
    ShowErrorMessage("Unable to fetch anime data");
}
```

### 3. **Caching Awareness**
```csharp
// Don't make rapid repeated requests for the same data
// The cache will handle this automatically, but be mindful of user experience
```

## Troubleshooting

### Common Issues

1. **No Results Returned**
   - Check internet connection
   - Verify Turkanime.co is accessible
   - Check Debug output for specific errors

2. **Slow Performance**
   - This is intentional to avoid being blocked
   - Use caching to minimize repeated requests
   - Consider using Fast configuration for development

3. **Parsing Errors**
   - Site structure may have changed
   - Check Debug output for parsing failures
   - Multiple fallback strategies should handle most changes

### Debug Information

The scraper provides detailed debug information:
- Request URLs and response codes
- Cache hits and misses
- Parsing successes and failures
- Retry attempts and delays

## Future Enhancements

The architecture supports easy addition of:
- CloudFlare bypass capabilities
- Proxy support
- Browser automation for JavaScript-heavy pages
- Additional anime providers
- Custom parsing strategies

## Migration from Original Scraper

To migrate from the original TurkAnimeScraper:

1. Replace `TurkAnimeService` with `EnhancedTurkAnimeService` in your ViewModels
2. Update any direct scraper usage to use the enhanced version
3. Add proper disposal in your components
4. Test thoroughly with your specific use cases

The enhanced scraper maintains the same interface, so migration should be seamless.
