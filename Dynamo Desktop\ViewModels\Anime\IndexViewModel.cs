﻿using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Services.Anime;
using ReactiveUI;
using ReactiveUI.Fody.Helpers;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;


namespace Dynamo_Desktop.ViewModels.Anime;

//not partial, added for refactoring
public class IndexViewModel : ViewModelBase
{

    private AnimeProviders _providers;

    public AnimeProviders Provider
    {
        get => _providers;
        set
        {
            this.RaiseAndSetIfChanged(ref _providers, value); InitService(); GetEpisodes();
        }
    }


    [Reactive]
    public List<PopularAnime> PopularAnime { get; set; } = new List<PopularAnime>();

    [Reactive]
    public List<PopularAnime> RecentAnime { get; set; } = new List<PopularAnime>();

    [Reactive]
    public List<PopularAnime> SearchResults { get; set; } = new List<PopularAnime>();

    [Reactive]
    public IAnimeService AnimeService { get; set; }

    [Reactive]
    public new string Sort { get; set; }

    private string _searchTerm = "";
    public string SearchTerm
    {
        get => _searchTerm; set
        {
            this.RaiseAndSetIfChanged(ref _searchTerm, value);
            SearchTermChanged();
        }
    }

    public List<string> SortOptions => new List<string>() { "Newest", "Popular" };
    public IndexViewModel()
    {
        Provider = AnimeProviders.TurkAnime; // Set default provider
        Sort = "Newest";
        Page = 1;
        InitService();
        GetEpisodes();
        
        // Start background updates
        _ = StartBackgroundUpdates();
    }
    
    private async Task StartBackgroundUpdates()
    {
        // Wait 30 seconds after initial load, then update every 5 minutes
        await Task.Delay(30000);
        
        while (true)
        {
            try
            {
                await Task.Delay(300000); // 5 minutes
                if (!DataLoading && string.IsNullOrEmpty(SearchTerm))
                {
                    Debug.WriteLine("Background update: Refreshing anime data");
                    GetEpisodes();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Background update error: {ex.Message}");
            }
        }
    }

    public void InitService()
    {
        switch (Provider)
        {
            case AnimeProviders.TurkAnime:
                AnimeService = new EnhancedTurkAnimeService();
                break;
            default:
                AnimeService = new EnhancedTurkAnimeService();
                break;
        }
    }
    public async void GetEpisodes()
    {
        if (AnimeService == null)
        {
            Debug.WriteLine("AnimeService is null");
            return;
        }
        
        DataLoading = true;
        try
        {
            // Load multiple pages simultaneously for more content
            var recentTasks = new List<Task<List<PopularAnime>>>();
            var popularTasks = new List<Task<List<PopularAnime>>>();
            
            // Load first 3 pages simultaneously
            for (int i = 1; i <= 3; i++)
            {
                recentTasks.Add(AnimeService.RecentAnime(i));
                popularTasks.Add(AnimeService.PopularAnime(i));
            }
            
            var recentResults = await Task.WhenAll(recentTasks);
            var popularResults = await Task.WhenAll(popularTasks);
            
            // Combine all results
            RecentAnime = recentResults.Where(r => r != null).SelectMany(r => r).ToList();
            PopularAnime = popularResults.Where(r => r != null).SelectMany(r => r).ToList();
            
            Debug.WriteLine($"Loaded {RecentAnime.Count} recent anime and {PopularAnime.Count} popular anime");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error fetching episodes: {ex.Message}");
            RecentAnime = new List<PopularAnime>();
            PopularAnime = new List<PopularAnime>();
        }
        finally
        {
            DataLoading = false;
        }
    }
    public void PrevPage()
    {
        if (Page > 1)
        {
            Page--;
            GetEpisodes();
        }
    }
    public void NextPage()
    {
        Page++;
        GetEpisodes();
    }
    
    public async void LoadMore()
    {
        if (DataLoading || AnimeService == null) return;
        
        DataLoading = true;
        try
        {
            // Load next 2 pages
            int startPage = (RecentAnime.Count / 20) + 1; // Assuming ~20 items per page
            var recentTasks = new List<Task<List<PopularAnime>>>();
            var popularTasks = new List<Task<List<PopularAnime>>>();
            
            for (int i = startPage; i <= startPage + 1; i++)
            {
                recentTasks.Add(AnimeService.RecentAnime(i));
                popularTasks.Add(AnimeService.PopularAnime(i));
            }
            
            var recentResults = await Task.WhenAll(recentTasks);
            var popularResults = await Task.WhenAll(popularTasks);
            
            // Append new results to existing lists
            var newRecent = recentResults.Where(r => r != null).SelectMany(r => r).ToList();
            var newPopular = popularResults.Where(r => r != null).SelectMany(r => r).ToList();
            
            RecentAnime = RecentAnime.Concat(newRecent).ToList();
            PopularAnime = PopularAnime.Concat(newPopular).ToList();
            
            Debug.WriteLine($"Loaded more: {newRecent.Count} recent, {newPopular.Count} popular anime");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error loading more anime: {ex.Message}");
        }
        finally
        {
            DataLoading = false;
        }
    }
    public void SearchTermChanged()
    {
        
        GeneralSearch();
    }
    public async void GeneralSearch()
    {
        if (string.IsNullOrWhiteSpace(SearchTerm))
        {
            SearchResults = new List<PopularAnime>();
            return;
        }
        
        DataLoading = true;
        try
        {
            // Search multiple pages for more results
            var searchTasks = new List<Task<List<PopularAnime>>>();
            for (int i = 1; i <= 2; i++)
            {
                searchTasks.Add(AnimeService.Search(SearchTerm, i));
            }
            
            var searchResults = await Task.WhenAll(searchTasks);
            SearchResults = searchResults.Where(r => r != null).SelectMany(r => r).ToList();
            
            Debug.WriteLine($"Found {SearchResults.Count} search results for '{SearchTerm}'");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error searching anime: {ex.Message}");
            SearchResults = new List<PopularAnime>();
        }
        finally
        {
            DataLoading = false;
        }
    }
}

