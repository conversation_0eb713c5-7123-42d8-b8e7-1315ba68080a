using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.VisualTree;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Services;
using Dynamo_Desktop.ViewModels.Anime;
using FluentAvalonia.UI.Controls;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Dynamo_Desktop.Views.Anime.Subviews
{
    public partial class DetailsSubView : UserControl
    {
        public DetailsSubView()
        {
            InitializeComponent();
            DataContext = new DetailsViewModel();
        }

        protected override void OnAttachedToVisualTree(VisualTreeAttachmentEventArgs e)
        {
            base.OnAttachedToVisualTree(e);
            Frame frame = this.FindAncestorOfType<Frame>();
            if (frame != null)
            {
                AnimeIndexToDetailsRouteParams routeParams = frame.Tag as AnimeIndexToDetailsRouteParams;
                (DataContext as DetailsViewModel).RouteParams = routeParams;
            }
           
        }
        private async void PresentVideo(object sender, TappedEventArgs e)
        {
            try
            {
                var border = sender as Border;
                if (border?.Tag == null)
                {
                    Debug.WriteLine("No video source available");
                    await ShowErrorDialog("No video source available", "Video Error");
                    return;
                }

                string source = border.Tag.ToString();
                if (string.IsNullOrWhiteSpace(source))
                {
                    Debug.WriteLine("Empty video source");
                    await ShowErrorDialog("Empty video source", "Video Error");
                    return;
                }

                Debug.WriteLine($"Attempting to play video: {source}");

                VideoService videoService = new();
                videoService.ProcessExited += (o, args) =>
                {
                    Debug.WriteLine("Video player exited");
                };

                try
                {
                    bool success = await videoService.Play(source);
                    if (success)
                    {
                        Debug.WriteLine("Video service Play() called successfully");
                    }
                    else
                    {
                        Debug.WriteLine("Video service Play() failed");
                        await ShowErrorDialog("Failed to start video player. Please ensure you have mpv or VLC installed.", "Video Player Error");
                    }
                }
                catch (Exception playEx)
                {
                    Debug.WriteLine($"Error in video service Play(): {playEx.Message}");
                    await ShowErrorDialog($"Failed to launch video player: {playEx.Message}", "Video Player Error");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error presenting video: {ex.Message}");
                await ShowErrorDialog($"Error presenting video: {ex.Message}", "Error");
            }
        }
        
        private async Task ShowErrorDialog(string content, string title)
        {
            var dialog = new ContentDialog()
            {
                Content = content,
                Title = title,
                DefaultButton = ContentDialogButton.Close,
                CloseButtonText = "OK"
            };
            await dialog.ShowAsync();
        }

        private void ChangeEpisode(object sender, TappedEventArgs e)
        {
            try
            {
                var border = sender as Border;
                var dataContext = DataContext as DetailsViewModel;
                
                if (border?.Tag == null || dataContext?.RouteParams == null)
                {
                    Debug.WriteLine("Invalid episode change request");
                    return;
                }
                
                if (!int.TryParse(border.Tag.ToString(), out int episodeNumber))
                {
                    Debug.WriteLine("Invalid episode number");
                    return;
                }
                
                AnimeIndexToDetailsRouteParams routeParams = new()
                {
                    Provider = dataContext.RouteParams.Provider,
                    AnimeId = dataContext.RouteParams.AnimeId,
                    EpisodeNumber = episodeNumber,
                };
                
                dataContext.RouteParams = routeParams;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error changing episode: {ex.Message}");
            }
        }
    }   
   
}
