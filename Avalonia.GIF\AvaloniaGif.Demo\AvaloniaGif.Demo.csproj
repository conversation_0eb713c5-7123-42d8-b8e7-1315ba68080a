﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net7.0</TargetFramework>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="**\*.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <EmbeddedResource Include="**\*.xaml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <AvaloniaResource Include="**\*.gif" />
  </ItemGroup>
  <ItemGroup> 
    <PackageReference Include="Avalonia" Version="11.1.0-beta2" />
    <PackageReference Include="Avalonia.Desktop" Version="11.1.0-beta2" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.1.0-beta2" />
    <ProjectReference Include="..\AvaloniaGif\AvaloniaGif.csproj" />
  </ItemGroup>
</Project>
