using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Services.Anime;
using Dynamo_Desktop.Services;

namespace StreamingDiagnostic
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Dynamo Streaming Diagnostic Tool ===");
            Console.WriteLine("This tool will help identify why you're getting 'no streams available' error.\n");
            
            // Test provider URLs first
            await TestProviderUrls();
            
            // Test AnimePahe
            await TestProvider("AnimePahe", new AnimePaheService());
            
            // Test GogoAnime  
            await TestProvider("GogoAnime", new GogoAnimeService());
            
            Console.WriteLine("\n=== Diagnostic Complete ===");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
        
        static async Task TestProviderUrls()
        {
            Console.WriteLine("--- Testing Provider URLs ---");
            
            try
            {
                var settings = SettingsService.GetSettings();
                
                await TestUrl("AnimePahe", settings.Providers.animepahe.host);
                await TestUrl("GogoAnime", settings.Providers.gogoanime.host);
                await TestUrl("ZoroAnime", settings.Providers.zoroanime.host);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error testing provider URLs: {ex.Message}");
            }
            
            Console.WriteLine();
        }
        
        static async Task TestUrl(string providerName, string url)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10);
                httpClient.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                
                var response = await httpClient.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"✅ {providerName} ({url}) - Accessible");
                }
                else
                {
                    Console.WriteLine($"❌ {providerName} ({url}) - HTTP {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ {providerName} ({url}) - Error: {ex.Message}");
            }
        }
        
        static async Task TestProvider(string providerName, IAnimeService service)
        {
            Console.WriteLine($"--- Testing {providerName} Provider ---");
            
            try
            {
                // Step 1: Test recent anime
                Console.WriteLine($"Step 1: Getting recent anime from {providerName}...");
                var recentAnime = await service.RecentAnime(1);
                
                if (recentAnime == null || recentAnime.Count == 0)
                {
                    Console.WriteLine($"❌ No recent anime found from {providerName}");
                    Console.WriteLine($"   This suggests the provider's recent anime endpoint is not working.");
                    return;
                }
                
                Console.WriteLine($"✅ Found {recentAnime.Count} recent anime from {providerName}");
                
                // Test with first anime
                var testAnime = recentAnime[0];
                Console.WriteLine($"   Testing with: '{testAnime.Title}' (ID: {testAnime.AnimeId})");
                
                // Step 2: Test anime info
                Console.WriteLine($"Step 2: Getting anime info for {testAnime.AnimeId}...");
                var animeInfo = await service.Info(testAnime.AnimeId);
                
                if (animeInfo == null)
                {
                    Console.WriteLine($"❌ Failed to get anime info for {testAnime.AnimeId}");
                    Console.WriteLine($"   This suggests the provider's info endpoint is not working.");
                    return;
                }
                
                if (animeInfo.Episodes == null || animeInfo.Episodes.Count == 0)
                {
                    Console.WriteLine($"❌ No episodes found for {testAnime.AnimeId}");
                    Console.WriteLine($"   The anime info was retrieved but contains no episodes.");
                    return;
                }
                
                Console.WriteLine($"✅ Found anime info with {animeInfo.Episodes.Count} episodes");
                
                // Step 3: Test streaming links
                Console.WriteLine($"Step 3: Getting streaming links for episode 1...");
                var streamingLinks = await service.StreamingLinks(testAnime.AnimeId, 1);
                
                if (streamingLinks == null || streamingLinks.Count == 0)
                {
                    Console.WriteLine($"❌ No streaming links found for {testAnime.AnimeId} episode 1");
                    Console.WriteLine($"   This is likely where your 'no streams available' error is coming from.");
                    
                    // Try a few more episodes
                    for (int ep = 2; ep <= Math.Min(3, animeInfo.Episodes.Count); ep++)
                    {
                        Console.WriteLine($"   Trying episode {ep}...");
                        var altLinks = await service.StreamingLinks(testAnime.AnimeId, ep);
                        if (altLinks != null && altLinks.Count > 0)
                        {
                            Console.WriteLine($"✅ Found {altLinks.Count} streaming links for episode {ep}");
                            await TestStreamingLinks(altLinks);
                            return;
                        }
                    }
                    
                    Console.WriteLine($"   No streaming links found for any episode of this anime.");
                    return;
                }
                
                Console.WriteLine($"✅ Found {streamingLinks.Count} streaming links for episode 1");
                await TestStreamingLinks(streamingLinks);
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error testing {providerName}: {ex.Message}");
                Console.WriteLine($"   Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
        }
        
        static async Task TestStreamingLinks(List<AnimeStreamingLinks> links)
        {
            Console.WriteLine("Step 4: Testing streaming link accessibility...");
            
            foreach (var link in links)
            {
                Console.WriteLine($"   Quality: {link.Quality}");
                Console.WriteLine($"   Source: {link.Source}");
                
                if (string.IsNullOrWhiteSpace(link.Source))
                {
                    Console.WriteLine($"   ❌ Empty source URL");
                    continue;
                }
                
                if (!IsValidUrl(link.Source))
                {
                    Console.WriteLine($"   ❌ Invalid URL format");
                    continue;
                }
                
                bool accessible = await TestStreamingUrl(link.Source);
                Console.WriteLine($"   Accessible: {(accessible ? "✅ Yes" : "❌ No")}");
                Console.WriteLine();
            }
        }
        
        static async Task<bool> TestStreamingUrl(string url)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10);
                httpClient.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                
                var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Head, url));
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }
        
        static bool IsValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out Uri result) 
                && (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }
    }
}
