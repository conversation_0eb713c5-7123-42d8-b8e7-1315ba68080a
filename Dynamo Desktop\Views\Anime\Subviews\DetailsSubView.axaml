<UserControl
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d"
    x:Class="Dynamo_Desktop.Views.Anime.Subviews.DetailsSubView"
    x:DataType="vm:DetailsViewModel"
    xmlns="https://github.com/avaloniaui"
    xmlns:anime="using:Dynamo_Desktop.Models.Anime"
    xmlns:asyncImageLoader="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:Dynamo_Desktop.ViewModels.Anime"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.DataContext>
        <vm:DetailsViewModel>
            <vm:DetailsViewModel.RouteParams>
                <anime:AnimeIndexToDetailsRouteParams AnimeId="death-note" Provider="GogoAnime" />
            </vm:DetailsViewModel.RouteParams>
        </vm:DetailsViewModel>
    </Design.DataContext>
    <ScrollViewer>
        <Grid RowDefinitions="Auto,Auto">
            <ProgressBar
                IsIndeterminate="True"
                IsVisible="{Binding DataLoading}"
                VerticalAlignment="Top"
                Width="1000" />
            <Grid
                Grid.Row="1"
                IsVisible="{Binding !DataLoading}"
                RowDefinitions="Auto,Auto">
                <Border Grid.Row="0" Height="300">
                    <Grid RowDefinitions="Auto,Auto">
                        <Panel Height="300">
                            <!--  Background image with blur effect  -->
                            <asyncImageLoader:AdvancedImage Source="{Binding Info.Image}" Stretch="Fill">
                                <asyncImageLoader:AdvancedImage.Effect>
                                    <BlurEffect Radius="20" />
                                </asyncImageLoader:AdvancedImage.Effect>
                            </asyncImageLoader:AdvancedImage>
                        </Panel>
                        <!--  Overlay content  -->
                        <Panel Height="300">
                            <Panel.Background>
                                <SolidColorBrush Color="Black" Opacity="0.6" />
                            </Panel.Background>
                            <asyncImageLoader:AdvancedImage
                                Height="300"
                                HorizontalAlignment="Center"
                                Source="{Binding Info.Image}" />
                        </Panel>
                    </Grid>
                </Border>
                <Grid ColumnDefinitions="*,*" Grid.Row="1">
                    <StackPanel
                        Grid.Column="0"
                        Margin="20"
                        Orientation="Vertical">
                        <WrapPanel Orientation="Horizontal">
                            <TextBlock
                                FontSize="40"
                                Text="{Binding Info.Title}"
                                TextTrimming="CharacterEllipsis" />
                            <Button
                                Classes="accent"
                                Command="{Binding GetInfo}"
                                Content="Refresh" />
                        </WrapPanel>
                        <TextBlock Text="{Binding Info.Description, FallbackValue='No description available'}" TextWrapping="Wrap" />
                    </StackPanel>
                    <StackPanel
                        Grid.Column="1"
                        Margin="20"
                        Orientation="Vertical">
                        <TextBlock FontSize="40" Text="{Binding RouteParams.EpisodeNumber, StringFormat='Stream Episode {0}'}" />
                        <ItemsRepeater ItemsSource="{Binding Links}">
                            <ItemsRepeater.Layout>
                                <UniformGridLayout/>
                            </ItemsRepeater.Layout>
                            <ItemsRepeater.ItemTemplate>
                                
                                <DataTemplate>
                                    <Border
                                        Background="{DynamicResource SystemAccentColor}"
                                        Margin="10"
                                        Padding="20"
                                        Tag="{Binding Source}"
                                        Tapped="PresentVideo">
                                        <TextBlock Text="{Binding Quality}" />
                                        <Border.Styles>
                                            <Styles>
                                                <Style Selector="Border:pointerover">
                                                    <Setter Property="Background" Value="{DynamicResource SystemAccentColorDark1}" />
                                                </Style>
                                            </Styles>
                                        </Border.Styles>
                                    </Border>
                                </DataTemplate>
                            </ItemsRepeater.ItemTemplate>
                        </ItemsRepeater>
                        <TextBlock FontSize="40" Text="{Binding Info.Episodes.Count, StringFormat='All Episodes ({0})'}" />
                        <ItemsRepeater ItemsSource="{Binding Info.Episodes}">
                            <ItemsRepeater.Layout>
                                <UniformGridLayout MaximumRowsOrColumns="10" />
                            </ItemsRepeater.Layout>
                            <ItemsRepeater.ItemTemplate>
                                <DataTemplate>
                                    <Border
                                        Background="{DynamicResource SystemAccentColor}"
                                        Margin="5"
                                        Padding="15"
                                        Tag="{Binding EpisodeNumber}"
                                        Tapped="ChangeEpisode">
                                        <TextBlock Text="{Binding EpisodeNumber}" HorizontalAlignment="Center" />
                                        <Border.Styles>
                                            <Styles>
                                                <Style Selector="Border:pointerover">
                                                    <Setter Property="Background" Value="{DynamicResource SystemAccentColorDark1}" />
                                                </Style>
                                            </Styles>
                                        </Border.Styles>
                                    </Border>
                                </DataTemplate>
                            </ItemsRepeater.ItemTemplate>
                        </ItemsRepeater>
                    </StackPanel>
                </Grid>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
