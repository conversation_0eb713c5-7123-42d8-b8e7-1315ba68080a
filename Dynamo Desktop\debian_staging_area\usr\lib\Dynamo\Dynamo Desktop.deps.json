{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/linux-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/linux-x64": {"Dynamo Desktop/1.0.0": {"dependencies": {"AsyncImageLoader.Avalonia": "3.2.1", "Avalonia": "11.1.0-beta2", "Avalonia.Controls.ItemsRepeater": "11.1.0-beta2", "Avalonia.Desktop": "11.1.0-beta2", "Avalonia.ReactiveUI": "11.1.0-beta2", "AvaloniaGif": "1.0.0", "CSharpFunctionalExtensions": "2.42.0", "FluentAvaloniaUI": "2.1.0-preview4", "HtmlAgilityPack": "1.11.61", "MessageBox.Avalonia": "*******", "Newtonsoft.Json": "13.0.3", "ReactiveUI.Fody": "19.5.41", "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64": "8.0.5"}, "runtime": {"Dynamo Desktop.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.5": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "********", "fileVersion": "13.0.524.21615"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.524.21615"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}}, "native": {"createdump": {"fileVersion": "0.0.0.0"}, "libSystem.Globalization.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.IO.Compression.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Net.Security.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.OpenSsl.so": {"fileVersion": "0.0.0.0"}, "libclrgc.so": {"fileVersion": "0.0.0.0"}, "libclrjit.so": {"fileVersion": "0.0.0.0"}, "libcoreclr.so": {"fileVersion": "0.0.0.0"}, "libcoreclrtraceptprovider.so": {"fileVersion": "0.0.0.0"}, "libhostfxr.so": {"fileVersion": "0.0.0.0"}, "libhostpolicy.so": {"fileVersion": "0.0.0.0"}, "libmscordaccore.so": {"fileVersion": "0.0.0.0"}, "libmscordbi.so": {"fileVersion": "0.0.0.0"}}}, "AsyncImageLoader.Avalonia/3.2.1": {"dependencies": {"Avalonia": "11.1.0-beta2"}, "runtime": {"lib/netstandard2.1/AsyncImageLoader.Avalonia.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Avalonia/11.1.0-beta2": {"dependencies": {"Avalonia.BuildServices": "0.0.29", "Avalonia.Remote.Protocol": "11.1.0-beta2", "MicroCom.Runtime": "0.11.0"}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Controls.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/Avalonia.Dialogs.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Markup.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.Metal.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.MicroCom.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.OpenGL.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/Avalonia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Angle.Windows.Natives/2.1.22045.20230930": {}, "Avalonia.BuildServices/0.0.29": {}, "Avalonia.Controls.ColorPicker/11.1.0-beta1": {"dependencies": {"Avalonia": "11.1.0-beta2", "Avalonia.Remote.Protocol": "11.1.0-beta2"}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Controls.DataGrid/11.1.0-beta1": {"dependencies": {"Avalonia": "11.1.0-beta2", "Avalonia.Remote.Protocol": "11.1.0-beta2"}, "runtime": {"lib/net8.0/Avalonia.Controls.DataGrid.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Controls.ItemsRepeater/11.1.0-beta2": {"dependencies": {"Avalonia": "11.1.0-beta2"}, "runtime": {"lib/net8.0/Avalonia.Controls.ItemsRepeater.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Desktop/11.1.0-beta2": {"dependencies": {"Avalonia": "11.1.0-beta2", "Avalonia.Native": "11.1.0-beta2", "Avalonia.Skia": "11.1.0-beta2", "Avalonia.Win32": "11.1.0-beta2", "Avalonia.X11": "11.1.0-beta2"}, "runtime": {"lib/net8.0/Avalonia.Desktop.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.FreeDesktop/11.1.0-beta2": {"dependencies": {"Avalonia": "11.1.0-beta2", "Tmds.DBus.Protocol": "0.16.0"}, "runtime": {"lib/net8.0/Avalonia.FreeDesktop.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Native/11.1.0-beta2": {"dependencies": {"Avalonia": "11.1.0-beta2"}, "runtime": {"lib/net8.0/Avalonia.Native.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.ReactiveUI/11.1.0-beta2": {"dependencies": {"Avalonia": "11.1.0-beta2", "ReactiveUI": "19.5.41", "System.Reactive": "6.0.1-preview.1"}, "runtime": {"lib/net8.0/Avalonia.ReactiveUI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Remote.Protocol/11.1.0-beta2": {"runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Skia/11.1.0-beta2": {"dependencies": {"Avalonia": "11.1.0-beta2", "HarfBuzzSharp": "*******", "HarfBuzzSharp.NativeAssets.Linux": "*******", "HarfBuzzSharp.NativeAssets.WebAssembly": "*******", "SkiaSharp": "2.88.8", "SkiaSharp.NativeAssets.Linux": "2.88.8", "SkiaSharp.NativeAssets.WebAssembly": "2.88.8"}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Win32/11.1.0-beta2": {"dependencies": {"Avalonia": "11.1.0-beta2", "Avalonia.Angle.Windows.Natives": "2.1.22045.20230930", "System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/net8.0/Avalonia.Win32.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.X11/11.1.0-beta2": {"dependencies": {"Avalonia": "11.1.0-beta2", "Avalonia.FreeDesktop": "11.1.0-beta2", "Avalonia.Skia": "11.1.0-beta2"}, "runtime": {"lib/net8.0/Avalonia.X11.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "ColorTextBlock.Avalonia/11.0.2": {"dependencies": {"Avalonia": "11.1.0-beta2"}, "runtime": {"lib/net6.0/ColorTextBlock.Avalonia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "CSharpFunctionalExtensions/2.42.0": {"runtime": {"lib/net6.0/CSharpFunctionalExtensions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "DialogHost.Avalonia/0.7.7": {"dependencies": {"Avalonia": "11.1.0-beta2", "System.Reactive": "6.0.1-preview.1"}, "runtime": {"lib/netstandard2.0/DialogHost.Avalonia.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DynamicData/8.3.27": {"dependencies": {"System.Reactive": "6.0.1-preview.1"}, "runtime": {"lib/net8.0/DynamicData.dll": {"assemblyVersion": "*******", "fileVersion": "8.3.27.26536"}}}, "FluentAvaloniaUI/2.1.0-preview4": {"dependencies": {"Avalonia": "11.1.0-beta2", "Avalonia.Controls.ColorPicker": "11.1.0-beta1", "Avalonia.Controls.DataGrid": "11.1.0-beta1", "Avalonia.Skia": "11.1.0-beta2", "MicroCom.Runtime": "0.11.0"}, "runtime": {"lib/net6.0/FluentAvalonia.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Fody/6.8.0": {}, "HarfBuzzSharp/*******": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "*******", "HarfBuzzSharp.NativeAssets.macOS": "*******"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"dependencies": {"HarfBuzzSharp": "*******"}, "native": {"runtimes/linux-x64/native/libHarfBuzzSharp.so": {"fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/*******": {}, "HarfBuzzSharp.NativeAssets.WebAssembly/*******": {}, "HarfBuzzSharp.NativeAssets.Win32/*******": {}, "HtmlAgilityPack/1.11.61": {"runtime": {"lib/netstandard2.0/HtmlAgilityPack.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Markdown.Avalonia.Tight/11.0.2": {"dependencies": {"Avalonia": "11.1.0-beta2", "ColorTextBlock.Avalonia": "11.0.2"}, "runtime": {"lib/net6.0/Markdown.Avalonia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MessageBox.Avalonia/*******": {"dependencies": {"Avalonia": "11.1.0-beta2", "DialogHost.Avalonia": "0.7.7", "Markdown.Avalonia.Tight": "11.0.2"}, "runtime": {"lib/netstandard2.0/MsBox.Avalonia.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MicroCom.Runtime/0.11.0": {"runtime": {"lib/net5.0/MicroCom.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "ReactiveUI/19.5.41": {"dependencies": {"DynamicData": "8.3.27", "Splat": "14.8.12", "System.ComponentModel.Annotations": "5.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/ReactiveUI.dll": {"assemblyVersion": "********", "fileVersion": "19.5.41.25387"}}}, "ReactiveUI.Fody/19.5.41": {"dependencies": {"Fody": "6.8.0", "ReactiveUI": "19.5.41"}, "runtime": {"lib/net8.0/ReactiveUI.Fody.Helpers.dll": {"assemblyVersion": "********", "fileVersion": "19.5.41.25387"}}}, "SkiaSharp/2.88.8": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.Linux/2.88.8": {"dependencies": {"SkiaSharp": "2.88.8"}, "native": {"runtimes/linux-x64/native/libSkiaSharp.so": {"fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {}, "SkiaSharp.NativeAssets.WebAssembly/2.88.8": {}, "SkiaSharp.NativeAssets.Win32/2.88.8": {}, "Splat/14.8.12": {"runtime": {"lib/net8.0/Splat.dll": {"assemblyVersion": "********", "fileVersion": "14.8.12.56860"}}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.IO.Pipelines/6.0.0": {"runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reactive/6.0.1-preview.1": {"runtime": {"lib/net6.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "Tmds.DBus.Protocol/0.16.0": {"dependencies": {"System.IO.Pipelines": "6.0.0"}, "runtime": {"lib/net6.0/Tmds.DBus.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AvaloniaGif/1.0.0": {"dependencies": {"Avalonia": "11.1.0-beta2", "System.Reactive": "6.0.1-preview.1"}, "runtime": {"AvaloniaGif.dll": {}}}}}, "libraries": {"Dynamo Desktop/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.5": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "AsyncImageLoader.Avalonia/3.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-m5q7mbGZ2s3gqQPYmzGQ4YjT/+CWAP1xHxWxuSSqbwdTVAZk86HYCHvrTCR6TxeXvkWafvTMuwJamooeEfeQQw==", "path": "asyncimageloader.avalonia/3.2.1", "hashPath": "asyncimageloader.avalonia.3.2.1.nupkg.sha512"}, "Avalonia/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-aBJUCT3nt7EKZ1SNTQlIl3/7pNiAZtMWaLnc7ii+DTGaOhTT682HGBTzJJaJOYJmXgnkxmqM9ACclxBN2fIOvQ==", "path": "avalonia/11.1.0-beta2", "hashPath": "avalonia.11.1.0-beta2.nupkg.sha512"}, "Avalonia.Angle.Windows.Natives/2.1.22045.20230930": {"type": "package", "serviceable": true, "sha512": "sha512-Bo3qOhKC1b84BIhiogndMdAzB3UrrESKK7hS769f5HWeoMw/pcd42US5KFYW2JJ4ZSTrXnP8mXwLTMzh+S+9Lg==", "path": "avalonia.angle.windows.natives/2.1.22045.20230930", "hashPath": "avalonia.angle.windows.natives.2.1.22045.20230930.nupkg.sha512"}, "Avalonia.BuildServices/0.0.29": {"type": "package", "serviceable": true, "sha512": "sha512-U4eJLQdoDNHXtEba7MZUCwrBErBTxFp6sUewXBOdAhU0Kwzwaa/EKFcYm8kpcysjzKtfB4S0S9n0uxKZFz/ikw==", "path": "avalonia.buildservices/0.0.29", "hashPath": "avalonia.buildservices.0.0.29.nupkg.sha512"}, "Avalonia.Controls.ColorPicker/11.1.0-beta1": {"type": "package", "serviceable": true, "sha512": "sha512-u8F/XfvB6sQP84trwjA4o6KaoBhzXb/hEFFWTkPQLLBnv/aQJPnEN/vsfTRo0e6eIG+JhyiP+Rrtc0E5iOtI6g==", "path": "avalonia.controls.colorpicker/11.1.0-beta1", "hashPath": "avalonia.controls.colorpicker.11.1.0-beta1.nupkg.sha512"}, "Avalonia.Controls.DataGrid/11.1.0-beta1": {"type": "package", "serviceable": true, "sha512": "sha512-+vvLq0H7dfW7rGKzXHb9d+KfzG/5xq9BiugUdKfVINCgCiGW+2VgwaVojtwDF5ekIrIxSgRy6DJ1ljFcr0vwKg==", "path": "avalonia.controls.datagrid/11.1.0-beta1", "hashPath": "avalonia.controls.datagrid.11.1.0-beta1.nupkg.sha512"}, "Avalonia.Controls.ItemsRepeater/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-hSb5KoqYfpv1LWs4h2pCsEm3BUZ9AQl4wAUIbTNiMmtk9mo1OPtoNz9iJCYv//sqiiTQG0/M/MbFh/a9EwGADA==", "path": "avalonia.controls.itemsrepeater/11.1.0-beta2", "hashPath": "avalonia.controls.itemsrepeater.11.1.0-beta2.nupkg.sha512"}, "Avalonia.Desktop/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-Fw2VP2PowJcYDo9/Xc195efDN8DijlhsSkUzpEV1RapOSWly5k1seLOSv//0X0K2A2aLUdl54z7EFCl4/OnjQg==", "path": "avalonia.desktop/11.1.0-beta2", "hashPath": "avalonia.desktop.11.1.0-beta2.nupkg.sha512"}, "Avalonia.FreeDesktop/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-5kakTWkIClL4eVQBr+UxCJLD8bJTDU44pf2CPnDOWuCI/X09+5n26aprV8lX43JhptlOsILwKiO7sYrft7Wbjg==", "path": "avalonia.freedesktop/11.1.0-beta2", "hashPath": "avalonia.freedesktop.11.1.0-beta2.nupkg.sha512"}, "Avalonia.Native/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-hWE86M3ts3q1PUVRCAemS/7P2LYF151pFpgQO5Z7ZIakoqMoZXb0DHCtbQlLcspMsXfjS/+4P18ntby+SOh8UQ==", "path": "avalonia.native/11.1.0-beta2", "hashPath": "avalonia.native.11.1.0-beta2.nupkg.sha512"}, "Avalonia.ReactiveUI/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-DfdsKv591Wyxt3USwcwiGbu8RQf3cbxEv48/JbEt7rLFxRhhgEJaA5FmcZzJek/O187g0dnytPPCwwa0VApnXg==", "path": "avalonia.reactiveui/11.1.0-beta2", "hashPath": "avalonia.reactiveui.11.1.0-beta2.nupkg.sha512"}, "Avalonia.Remote.Protocol/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-Es8Azqf0g/caj1CUWVbY5qh9ieUOdg8oCen6uJv4VYJQGUcSIbCvqTUS4U3Jp6S/ql1hwnqpARNGyFQ50uG92A==", "path": "avalonia.remote.protocol/11.1.0-beta2", "hashPath": "avalonia.remote.protocol.11.1.0-beta2.nupkg.sha512"}, "Avalonia.Skia/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-3NmLdAH2PARZ6GA5nzLG+YEUjpLETFF1BmsaJiDdcpGgB0FZnNVxsCoAgmzrCLxpEZ5GTOhPOsS+qEm4QpGxng==", "path": "avalonia.skia/11.1.0-beta2", "hashPath": "avalonia.skia.11.1.0-beta2.nupkg.sha512"}, "Avalonia.Win32/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-lEjXn0d9+2GFVVI2bAjolGEqcVIviaeKiy2rhPQ9b+KoMQEDTFpsIbN0m8K3K0fXZpzN9zCb2CmXiXKHu9RBsA==", "path": "avalonia.win32/11.1.0-beta2", "hashPath": "avalonia.win32.11.1.0-beta2.nupkg.sha512"}, "Avalonia.X11/11.1.0-beta2": {"type": "package", "serviceable": true, "sha512": "sha512-wf3pSW0GSrOfvpSLrCiSH9f6Ky336/rtmtxN3R5pZMMI4zha+ISDcuV0PAVvmHzn1328xw4Phxdd+b8oV4wL4Q==", "path": "avalonia.x11/11.1.0-beta2", "hashPath": "avalonia.x11.11.1.0-beta2.nupkg.sha512"}, "ColorTextBlock.Avalonia/11.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-MsWu5aAbDpstAmJ+TTSgC6Q7YdjKB7Na912GMr3oWl9ag8TH2HNpglzgo+kIAuB4C4wswKfrzhso4UcpvgMuHg==", "path": "colortextblock.avalonia/11.0.2", "hashPath": "colortextblock.avalonia.11.0.2.nupkg.sha512"}, "CSharpFunctionalExtensions/2.42.0": {"type": "package", "serviceable": true, "sha512": "sha512-T2L6CXuXUNWG19YhnEk5jgfwiW6xNl+Vm5OL2W2yTRgTVH8zi5F7kOI3sgN57IqNkhGetjFn9Xd570gvXlEP1Q==", "path": "csharpfunctionalextensions/2.42.0", "hashPath": "csharpfunctionalextensions.2.42.0.nupkg.sha512"}, "DialogHost.Avalonia/0.7.7": {"type": "package", "serviceable": true, "sha512": "sha512-V5zq0e6dBeK1A8xDsyNouzfSx3d+aOhxPgjguaFyaDexgDF8WiSQDsawkv0fRGKbDnaPIqMk9m7P/2G5c7HPzw==", "path": "dialoghost.avalonia/0.7.7", "hashPath": "dialoghost.avalonia.0.7.7.nupkg.sha512"}, "DynamicData/8.3.27": {"type": "package", "serviceable": true, "sha512": "sha512-m5PGnTbFCE04xrH3peeNtiHrXJM5Vxk6Sy9bwXm8ahZ+/ZavqYb8XstBD1GFqcU7g6f8YD3zPcI7XkkW3Y1Ang==", "path": "dynamicdata/8.3.27", "hashPath": "dynamicdata.8.3.27.nupkg.sha512"}, "FluentAvaloniaUI/2.1.0-preview4": {"type": "package", "serviceable": true, "sha512": "sha512-ubEMP6KjbOUloSYYBywqolcJ6Wm/E14hfniEi1Ey/agkfJ36+XHjY3ab+U3HuWZDEn8t76dzdtO/cberLoIBJQ==", "path": "fluentavaloniaui/2.1.0-preview4", "hashPath": "fluentavaloniaui.2.1.0-preview4.nupkg.sha512"}, "Fody/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-hfZ/f8Mezt8aTkgv9nsvFdYoQ809/AqwsJlOGOPYIfBcG2aAIG3v3ex9d8ZqQuFYyMoucjRg4eKy3VleeGodKQ==", "path": "fody/6.8.0", "hashPath": "fody.6.8.0.nupkg.sha512"}, "HarfBuzzSharp/*******": {"type": "package", "serviceable": true, "sha512": "sha512-0tCd6HyCmNsX/DniCp2b00fo0xPbdNwKOs9BxxyT8oOOuMlWjcSFwzONKyeckCKVBFEsbSmsAHPDTqxoSDwZMg==", "path": "harfbuzzsharp/*******", "hashPath": "harfbuzzsharp.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"type": "package", "serviceable": true, "sha512": "sha512-aKa5J1RqjXKAtdcZJp5wjC78klfBIzJHM6CneN76lFmQ9LLRJA9Oa0TkIDaV8lVLDKMAy5fCKHXFlXUK1YfL/g==", "path": "harfbuzzsharp.nativeassets.linux/*******", "hashPath": "harfbuzzsharp.nativeassets.linux.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"type": "package", "serviceable": true, "sha512": "sha512-nycYH/WLJ6ogm+I+QSFCdPJsdxSb5GANWYbQyp1vsd/KjXN56RVUJWPhbgP2GKb/Y7mrsHM7EProqVXlO/EMsA==", "path": "harfbuzzsharp.nativeassets.macos/*******", "hashPath": "harfbuzzsharp.nativeassets.macos.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.WebAssembly/*******": {"type": "package", "serviceable": true, "sha512": "sha512-pR6H16NOAjIhfXomYsgN5anGbOHSfIA8z0RoWEVx8vgLySxz9Jie4DnQ/paWTh5MTtBC8XKkTUI7uKMXcq/Pxw==", "path": "harfbuzzsharp.nativeassets.webassembly/*******", "hashPath": "harfbuzzsharp.nativeassets.webassembly.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"type": "package", "serviceable": true, "sha512": "sha512-DpF9JBzwws2dupOLnjME65hxQWWbN/GD40AoTkwB4S05WANvxo3n81AnQJKxWDCnrWfWhLPB36OF27TvEqzb/A==", "path": "harfbuzzsharp.nativeassets.win32/*******", "hashPath": "harfbuzzsharp.nativeassets.win32.*******.nupkg.sha512"}, "HtmlAgilityPack/1.11.61": {"type": "package", "serviceable": true, "sha512": "sha512-hqZASeEFHS9quHvfZSwaULoAJLWkOYVPiQjc3P9J4pQS8vSYzrP3bxe04Vm7vYYuxwbQhq9hCSVvZVLTyRaNaQ==", "path": "htmlagilitypack/1.11.61", "hashPath": "htmlagilitypack.1.11.61.nupkg.sha512"}, "Markdown.Avalonia.Tight/11.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ylg0EkSUId42dVUFQvU7UJGWcKiJ+g7TesRe/KCzUYzlypWf1lTiVveT2j60QSldCtuAw3o/aEOEjIahMdRGig==", "path": "markdown.avalonia.tight/11.0.2", "hashPath": "markdown.avalonia.tight.11.0.2.nupkg.sha512"}, "MessageBox.Avalonia/*******": {"type": "package", "serviceable": true, "sha512": "sha512-iE6AzefOgcNHwdmQoUIeuii09UsFzMJoo027dZ/oKXmIaX9C8isTejODlGsuRmWgvvgMNULXsQTaH0VknMSEnw==", "path": "messagebox.avalonia/*******", "hashPath": "messagebox.avalonia.*******.nupkg.sha512"}, "MicroCom.Runtime/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "path": "microcom.runtime/0.11.0", "hashPath": "microcom.runtime.0.11.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "ReactiveUI/19.5.41": {"type": "package", "serviceable": true, "sha512": "sha512-qusIoJHhzNFm/Ei52wwesy15le/NAMWm40UOz2boaWcBntqOJJPR6YucDUibHNo5v9opNP7ZCAMa+w3rzX6AIw==", "path": "reactiveui/19.5.41", "hashPath": "reactiveui.19.5.41.nupkg.sha512"}, "ReactiveUI.Fody/19.5.41": {"type": "package", "serviceable": true, "sha512": "sha512-bh6OH1WsSHf/ryUVaPnI6LWl1+lrCvq4L/AoHpzPXCKxtR4p0ogPVONnkq0jYgSQ55x7YNCVkthBKeb6uUSbpg==", "path": "reactiveui.fody/19.5.41", "hashPath": "reactiveui.fody.19.5.41.nupkg.sha512"}, "SkiaSharp/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "path": "skiasharp/2.88.8", "hashPath": "skiasharp.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-0FO6YA7paNFBMJULvEyecPmCvL9/STvOAi5VOUw2srqJ7pNTbiiZkfl7sulAzcumbWgfzaVjRXYTgMj7SoUnWQ==", "path": "skiasharp.nativeassets.linux/2.88.8", "hashPath": "skiasharp.nativeassets.linux.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "path": "skiasharp.nativeassets.macos/2.88.8", "hashPath": "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.WebAssembly/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-S3qRo8c+gVYOyfrdf6FYnjx/ft+gPkb4dNY2IPv5Oy5yNBhDhXhKqHFr9h4+ne6ZU+7D4dbuRQqsIqCo8u1/DA==", "path": "skiasharp.nativeassets.webassembly/2.88.8", "hashPath": "skiasharp.nativeassets.webassembly.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "path": "skiasharp.nativeassets.win32/2.88.8", "hashPath": "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512"}, "Splat/14.8.12": {"type": "package", "serviceable": true, "sha512": "sha512-7dTp+hgC2CS4DqjHtgkeS7hsECbpD09ukGwxL63HVM/aih9zseIaIjWhS6sQWlWw5GDvli8bVMHeZ1dekgmuvQ==", "path": "splat/14.8.12", "hashPath": "splat.14.8.12.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mXX66shZ4xLlI3vNLaJ0lt8OIZdmXTvIqXRdQX5HLVGSkLhINLsVhyZuX2UdRFnOGkqnwmMUs40pIIQ7mna4+A==", "path": "system.io.pipelines/6.0.0", "hashPath": "system.io.pipelines.6.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reactive/6.0.1-preview.1": {"type": "package", "serviceable": true, "sha512": "sha512-3+0YK2nGSOs8VNL56B6YzCE70RO1f8QdmVu7/w3ifnY++SBihgKaM7QWYDgAL8TPTltNhU2Mtxc/5gLwGh1KyA==", "path": "system.reactive/6.0.1-preview.1", "hashPath": "system.reactive.6.0.1-preview.1.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "Tmds.DBus.Protocol/0.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-9Ne9AvrRWrDQvzbMcbTz3EaGRE1vQmkeeu0Xp7Ii0Wdlhwfjst8FDRas+RBslU5kdPWs2aeMLErzyT1DwH9zeA==", "path": "tmds.dbus.protocol/0.16.0", "hashPath": "tmds.dbus.protocol.0.16.0.nupkg.sha512"}, "AvaloniaGif/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}, "runtimes": {"android-x64": ["android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-bionic-x64": ["linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-musl-x64": ["linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-x64": ["linux", "unix-x64", "unix", "any", "base"], "ubuntu.24.04-x64": ["linux-x64", "linux", "unix-x64", "unix", "any", "base"]}}