using System;
using System.Diagnostics;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Layout;
using Avalonia.Markup.Xaml;
using Avalonia.VisualTree;
using Dynamo_Desktop.Services;
using Dynamo_Desktop.ViewModels.Hentai;
using FluentAvalonia.UI.Controls;

namespace Dynamo_Desktop.Views.Hentai.SubViews;

public partial class DetailsSubView : UserControl
{
    public DetailsSubView()
    {
        InitializeComponent();
        DataContext = new DetailsViewModel();
        TagsList.AttachedToVisualTree += (sender, args) =>
        {
            TagsList.Layout = new WrapLayout();
        };
        ServerList.AttachedToVisualTree += (sender, args) =>
        {
          //  ServerList.Layout = new WrapLayout();
        };
    }
    protected override void OnAttachedToVisualTree(VisualTreeAttachmentEventArgs e)
    {
        base.OnAttachedToVisualTree(e);
        Frame frame = this.FindAncestorOfType<Frame>();
        if(frame != null)
        {
            HentaiIndexToDetailsRouteParams routeParams = frame.Tag as HentaiIndexToDetailsRouteParams;
            (DataContext as DetailsViewModel).RouteParams = routeParams;
        }
    }
    public async void PlayVideo(object sender, RoutedEventArgs e)
    {
        try
        {
            string link = (sender as Button).Tag.ToString();
            if (string.IsNullOrWhiteSpace(link))
            {
                Debug.WriteLine("No video link available");
                return;
            }

            VideoService videoService = new();
            videoService.ProcessExited += (o, args) =>
            {
                Debug.WriteLine("Video was exited");
            };

            bool success = await videoService.Play(link);
            if (!success)
            {
                Debug.WriteLine("Failed to start video player");
                // Could show a dialog here if needed
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error playing video: {ex.Message}");
        }
    }
}
