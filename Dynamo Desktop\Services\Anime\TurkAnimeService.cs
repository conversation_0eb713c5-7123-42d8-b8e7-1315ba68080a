using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text.Json;
using System.Threading.Tasks;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Scrapers.Anime;

namespace Dynamo_Desktop.Services.Anime
{
    public class TurkAnimeService : IAnimeService
    {
        public async Task<List<PopularAnime>> RecentAnime(int Page = 1)
        {
            Debug.WriteLine($"TurkAnimeService: Fetching recent anime for page {Page}");
            
            try
            {
                var scraperResult = await new TurkAnimeScraper().RecentAnime(Page);
                Debug.WriteLine($"TurkAnimeService: Scraper returned: {scraperResult}");
                
                if (string.IsNullOrWhiteSpace(scraperResult))
                {
                    Debug.WriteLine("TurkAnimeService: Empty result from scraper");
                    return new List<PopularAnime>();
                }
                
                var result = JsonSerializer.Deserialize<List<PopularAnime>>(scraperResult);
                if (result == null)
                {
                    Debug.WriteLine("TurkAnimeService: Failed to deserialize recent anime");
                    return new List<PopularAnime>();
                }
                
                Debug.WriteLine($"TurkAnimeService: Found {result.Count} recent anime");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TurkAnimeService: Exception in RecentAnime: {ex.Message}");
                return new List<PopularAnime>();
            }
        }

        public async Task<List<PopularAnime>> PopularAnime(int Page = 1)
        {
            Debug.WriteLine($"TurkAnimeService: PopularAnime redirecting to RecentAnime for page {Page}");
            
            // TurkAnime doesn't have a separate popular endpoint, use recent
            return await RecentAnime(Page);
        }

        public async Task<List<PopularAnime>> Search(string Query, int Page = 1)
        {
            Debug.WriteLine($"TurkAnimeService: Searching for '{Query}' on page {Page}");

            try
            {
                var scraperResult = await new TurkAnimeScraper().Search(Query);
                Debug.WriteLine($"TurkAnimeService: Search scraper returned: {scraperResult}");

                if (string.IsNullOrWhiteSpace(scraperResult))
                {
                    Debug.WriteLine("TurkAnimeService: Empty search result from scraper");
                    return new List<PopularAnime>();
                }

                var result = JsonSerializer.Deserialize<List<PopularAnime>>(scraperResult);
                if (result == null)
                {
                    Debug.WriteLine("TurkAnimeService: Failed to deserialize search results");
                    return new List<PopularAnime>();
                }

                Debug.WriteLine($"TurkAnimeService: Found {result.Count} search results");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TurkAnimeService: Exception in Search: {ex.Message}");
                return new List<PopularAnime>();
            }
        }

        public async Task<AnimeInfo> Info(string Query)
        {
            Debug.WriteLine($"TurkAnimeService: Fetching info for '{Query}'");
            
            try
            {
                var scraperResult = await new TurkAnimeScraper().AnimeInfo(Query);
                Debug.WriteLine($"TurkAnimeService: Info scraper returned: {scraperResult}");
                
                if (string.IsNullOrWhiteSpace(scraperResult))
                {
                    Debug.WriteLine("TurkAnimeService: Empty info result from scraper");
                    return new AnimeInfo();
                }
                
                var result = JsonSerializer.Deserialize<AnimeInfo>(scraperResult);
                if (result == null)
                {
                    Debug.WriteLine("TurkAnimeService: Failed to deserialize anime info");
                    return new AnimeInfo();
                }
                
                Debug.WriteLine($"TurkAnimeService: Found anime info with {result.Episodes?.Count ?? 0} episodes");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TurkAnimeService: Exception in Info: {ex.Message}");
                return new AnimeInfo();
            }
        }

        public async Task<List<AnimeStreamingLinks>> StreamingLinks(string Query, int Episode = 1)
        {
            Debug.WriteLine($"TurkAnimeService: Fetching streaming links for '{Query}' episode {Episode}");
            
            try
            {
                var scraperResult = await new TurkAnimeScraper().EpisodeStreamLinks(Query, Episode);
                Debug.WriteLine($"TurkAnimeService: Streaming links scraper returned: {scraperResult}");
                
                if (string.IsNullOrWhiteSpace(scraperResult))
                {
                    Debug.WriteLine("TurkAnimeService: Empty streaming links result from scraper");
                    return new List<AnimeStreamingLinks>();
                }
                
                var result = JsonSerializer.Deserialize<List<AnimeStreamingLinks>>(scraperResult);
                if (result == null)
                {
                    Debug.WriteLine("TurkAnimeService: Failed to deserialize streaming links");
                    return new List<AnimeStreamingLinks>();
                }
                
                Debug.WriteLine($"TurkAnimeService: Found {result.Count} streaming links");
                foreach (var link in result)
                {
                    Debug.WriteLine($"  - Quality: {link.Quality}, Source: {link.Source}");
                }
                
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"TurkAnimeService: Exception in StreamingLinks: {ex.Message}");
                return new List<AnimeStreamingLinks>();
            }
        }
    }
}
