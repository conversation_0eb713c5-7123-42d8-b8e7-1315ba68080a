<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<LangVersion>11</LangVersion>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
		<Nullable>enable</Nullable>
		<!-- Suppress warnings for third-party library -->
		<NoWarn>$(NoWarn);CS8618;CS8602;CS8603;CS8604;CS8625;CS8765;CS0169;SYSLIB0051</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <InternalsVisibleTo Include="AvaloniaGif.Test" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.1.0-beta2" />
    <PackageReference Include="System.Reactive" Version="6.0.1-preview.1" />
  </ItemGroup>

</Project>
