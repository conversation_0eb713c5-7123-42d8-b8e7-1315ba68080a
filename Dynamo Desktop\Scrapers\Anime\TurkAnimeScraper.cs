using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Services;
using HtmlAgilityPack;

namespace Dynamo_Desktop.Scrapers.Anime
{
    public class TurkAnimeScraper
    {
        private string Host => SettingsService.GetSettings().Providers.turkanime.host;

        public async Task<string> RecentAnime(int Page = 1)
        {
            var handler = new HttpClientHandler();
            handler.UseCookies = false;
            handler.AutomaticDecompression = ~DecompressionMethods.None;
            
            List<PopularAnime> recentAnime = new List<PopularAnime>();
            
            using (var httpClient = new HttpClient(handler))
            {
                httpClient.Timeout = TimeSpan.FromSeconds(30);
                
                using (var request = new HttpRequestMessage(new HttpMethod("GET"), $"{Host}/?sayfa={Page}"))
                {
                    request.Headers.TryAddWithoutValidation("User-Agent", 
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
                    request.Headers.TryAddWithoutValidation("Accept", 
                        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
                    request.Headers.TryAddWithoutValidation("Accept-Language", "tr-TR,tr;q=0.9,en;q=0.8");
                    request.Headers.TryAddWithoutValidation("Referer", Host);
                    
                    try
                    {
                        var response = await httpClient.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var responseBody = await response.Content.ReadAsStringAsync();
                            HtmlDocument htmlDoc = new HtmlDocument();
                            htmlDoc.LoadHtml(responseBody);
                            
                            // Look for recent episodes - TurkAnime shows recent episodes on homepage
                            // Try multiple selectors to find episode/anime links
                            var episodeNodes = htmlDoc.DocumentNode.SelectNodes("//a[contains(@href, '/video/')]") ??
                                             htmlDoc.DocumentNode.SelectNodes("//a[contains(@href, '/anime/')]");

                            if (episodeNodes != null)
                            {
                                var processedAnime = new HashSet<string>();

                                foreach (var node in episodeNodes.Take(30)) // Get more items to filter
                                {
                                    try
                                    {
                                        var href = node.GetAttributeValue("href", "");
                                        var title = node.GetAttributeValue("title", "") ??
                                                   node.InnerText?.Trim() ??
                                                   node.SelectSingleNode(".//text()")?.InnerText?.Trim();

                                        if (!string.IsNullOrEmpty(href) && !string.IsNullOrEmpty(title))
                                        {
                                            // Extract anime ID from URL
                                            var animeId = ExtractAnimeIdFromUrl(href);
                                            if (!string.IsNullOrEmpty(animeId) && processedAnime.Add(animeId))
                                            {
                                                // Try to get image from nearby img tag or parent container
                                                var imageUrl = "";
                                                var imgNode = node.SelectSingleNode(".//img") ??
                                                             node.SelectSingleNode("preceding-sibling::img[1]") ??
                                                             node.SelectSingleNode("following-sibling::img[1]") ??
                                                             node.ParentNode?.SelectSingleNode(".//img");

                                                if (imgNode != null)
                                                {
                                                    imageUrl = imgNode.GetAttributeValue("src", "") ??
                                                              imgNode.GetAttributeValue("data-src", "");
                                                }

                                                // Extract episode number if it's a video link
                                                int episodeNumber = 1;
                                                if (href.Contains("/video/"))
                                                {
                                                    var episodeMatch = Regex.Match(href, @"-(\d+)(?:\?|$)");
                                                    if (episodeMatch.Success)
                                                    {
                                                        int.TryParse(episodeMatch.Groups[1].Value, out episodeNumber);
                                                    }
                                                }

                                                recentAnime.Add(new PopularAnime
                                                {
                                                    Title = CleanTitle(title),
                                                    AnimeId = animeId,
                                                    Image = !string.IsNullOrEmpty(imageUrl) ? MakeAbsoluteUrl(imageUrl) : "",
                                                    Episode = episodeNumber
                                                });

                                                if (recentAnime.Count >= 20) break; // Limit results
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Debug.WriteLine($"Error parsing episode node: {ex.Message}");
                                    }
                                }
                            }
                            
                            // If no episodes found, try alternative selectors
                            if (recentAnime.Count == 0)
                            {
                                var alternativeNodes = htmlDoc.DocumentNode.SelectNodes("//a[contains(@href, 'anime/')]");
                                if (alternativeNodes != null)
                                {
                                    foreach (var node in alternativeNodes.Take(10))
                                    {
                                        try
                                        {
                                            var href = node.GetAttributeValue("href", "");
                                            var title = node.GetAttributeValue("title", "") ?? node.InnerText?.Trim();
                                            
                                            if (!string.IsNullOrEmpty(href) && !string.IsNullOrEmpty(title) && href.Contains("anime/"))
                                            {
                                                var animeId = ExtractAnimeIdFromUrl(href);
                                                if (!string.IsNullOrEmpty(animeId))
                                                {
                                                    recentAnime.Add(new PopularAnime
                                                    {
                                                        Title = CleanTitle(title),
                                                        AnimeId = animeId,
                                                        Image = "",
                                                        Episode = 1
                                                    });
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Debug.WriteLine($"Error parsing alternative node: {ex.Message}");
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"TurkAnime request failed with status: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error fetching recent anime from TurkAnime: {ex.Message}");
                    }
                }
            }
            
            return JsonSerializer.Serialize(recentAnime);
        }

        public async Task<string> Search(string Query)
        {
            var handler = new HttpClientHandler();
            handler.UseCookies = false;
            handler.AutomaticDecompression = ~DecompressionMethods.None;
            
            List<PopularAnime> searchResults = new List<PopularAnime>();
            
            using (var httpClient = new HttpClient(handler))
            {
                httpClient.Timeout = TimeSpan.FromSeconds(30);
                
                string searchUrl = $"{Host}/arama?arama={Uri.EscapeDataString(Query)}";
                
                using (var request = new HttpRequestMessage(new HttpMethod("GET"), searchUrl))
                {
                    request.Headers.TryAddWithoutValidation("User-Agent", 
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
                    request.Headers.TryAddWithoutValidation("Accept", 
                        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
                    request.Headers.TryAddWithoutValidation("Accept-Language", "tr-TR,tr;q=0.9,en;q=0.8");
                    request.Headers.TryAddWithoutValidation("Referer", Host);
                    
                    try
                    {
                        var response = await httpClient.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var responseBody = await response.Content.ReadAsStringAsync();
                            HtmlDocument htmlDoc = new HtmlDocument();
                            htmlDoc.LoadHtml(responseBody);
                            
                            // Look for search results
                            var resultNodes = htmlDoc.DocumentNode.SelectNodes("//a[contains(@href, 'anime/')]");
                            
                            if (resultNodes != null)
                            {
                                foreach (var node in resultNodes.Take(20))
                                {
                                    try
                                    {
                                        var href = node.GetAttributeValue("href", "");
                                        var title = node.GetAttributeValue("title", "") ?? node.InnerText?.Trim();
                                        
                                        if (!string.IsNullOrEmpty(href) && !string.IsNullOrEmpty(title))
                                        {
                                            var animeId = ExtractAnimeIdFromUrl(href);
                                            if (!string.IsNullOrEmpty(animeId))
                                            {
                                                var imgNode = node.SelectSingleNode(".//img");
                                                var imageUrl = imgNode?.GetAttributeValue("src", "") ?? "";
                                                
                                                searchResults.Add(new PopularAnime
                                                {
                                                    Title = CleanTitle(title),
                                                    AnimeId = animeId,
                                                    Image = !string.IsNullOrEmpty(imageUrl) ? MakeAbsoluteUrl(imageUrl) : "",
                                                    Episode = 1
                                                });
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Debug.WriteLine($"Error parsing search result: {ex.Message}");
                                    }
                                }
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"TurkAnime search failed with status: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error searching TurkAnime: {ex.Message}");
                    }
                }
            }
            
            return JsonSerializer.Serialize(searchResults);
        }

        private string ExtractAnimeIdFromUrl(string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url)) return "";

                // Handle relative URLs
                if (url.StartsWith("/"))
                {
                    url = Host + url;
                }

                // Extract anime ID from URL patterns like:
                // /anime/anime-name
                // /video/anime-name-episode-1
                var match = Regex.Match(url, @"/anime/([^/]+)");
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }

                // Try video URL pattern - need to remove episode number and "bolum"
                match = Regex.Match(url, @"/video/([^/]+)");
                if (match.Success)
                {
                    var fullId = match.Groups[1].Value;

                    // Remove episode number and "bolum" from the end
                    // Pattern: anime-name-episode-bolum -> anime-name
                    var cleanedId = Regex.Replace(fullId, @"-\d+-bolum$", "");
                    if (cleanedId != fullId)
                    {
                        return cleanedId;
                    }

                    // Alternative pattern: anime-name-episode -> anime-name
                    cleanedId = Regex.Replace(fullId, @"-\d+$", "");
                    return cleanedId;
                }

                return "";
            }
            catch
            {
                return "";
            }
        }

        private string CleanTitle(string title)
        {
            if (string.IsNullOrEmpty(title)) return "";
            
            // Remove common suffixes and clean up
            title = title.Trim();
            title = Regex.Replace(title, @"\s+", " "); // Multiple spaces to single space
            title = Regex.Replace(title, @"\d+\.\s*Bölüm.*$", "", RegexOptions.IgnoreCase); // Remove episode info
            title = Regex.Replace(title, @"izle.*$", "", RegexOptions.IgnoreCase); // Remove "izle" suffix
            
            return title.Trim();
        }

        private string MakeAbsoluteUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return "";

            if (url.StartsWith("http"))
            {
                return url;
            }
            else if (url.StartsWith("/"))
            {
                return Host + url;
            }
            else
            {
                return Host + "/" + url;
            }
        }

        public async Task<string> AnimeInfo(string Query)
        {
            var handler = new HttpClientHandler();
            handler.UseCookies = false;
            handler.AutomaticDecompression = ~DecompressionMethods.None;

            AnimeInfo animeInfo = new AnimeInfo();

            using (var httpClient = new HttpClient(handler))
            {
                httpClient.Timeout = TimeSpan.FromSeconds(30);

                string animeUrl = $"{Host}/anime/{Query}";

                using (var request = new HttpRequestMessage(new HttpMethod("GET"), animeUrl))
                {
                    request.Headers.TryAddWithoutValidation("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
                    request.Headers.TryAddWithoutValidation("Accept",
                        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
                    request.Headers.TryAddWithoutValidation("Accept-Language", "tr-TR,tr;q=0.9,en;q=0.8");
                    request.Headers.TryAddWithoutValidation("Referer", Host);

                    try
                    {
                        var response = await httpClient.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var responseBody = await response.Content.ReadAsStringAsync();
                            HtmlDocument htmlDoc = new HtmlDocument();
                            htmlDoc.LoadHtml(responseBody);

                            // Extract title from h1 or page title
                            var titleNode = htmlDoc.DocumentNode.SelectSingleNode("//h1") ??
                                           htmlDoc.DocumentNode.SelectSingleNode("//title");
                            animeInfo.Title = titleNode?.InnerText?.Trim().Replace(" izle", "").Replace("-->", "").Trim() ?? Query;

                            // Extract description from Özet section or JSON-LD
                            var descriptionText = "";

                            // First try JSON-LD description
                            var jsonLdDescMatch = Regex.Match(responseBody, @"""description"":\s*""([^""]+)""");
                            if (jsonLdDescMatch.Success)
                            {
                                descriptionText = jsonLdDescMatch.Groups[1].Value;
                                // Decode any escaped characters
                                descriptionText = descriptionText.Replace("\\\"", "\"").Replace("\\n", "\n").Replace("\\r", "");
                            }
                            else
                            {
                                // Fallback: try to find Özet section in the HTML
                                var ozetMatch = Regex.Match(responseBody, @"Özet;\s*([^{]+?)(?=\{|$)", RegexOptions.Singleline);
                                if (ozetMatch.Success)
                                {
                                    descriptionText = ozetMatch.Groups[1].Value.Trim();
                                    // Clean up common endings
                                    var endMarkers = new[] { "Disqus Yorumlar", "FANSUB DUYURU", "BEĞENİLEN BÖLÜMLER" };
                                    foreach (var marker in endMarkers)
                                    {
                                        var endIndex = descriptionText.IndexOf(marker);
                                        if (endIndex > 0)
                                        {
                                            descriptionText = descriptionText.Substring(0, endIndex).Trim();
                                            break;
                                        }
                                    }
                                }
                            }

                            animeInfo.Description = descriptionText;

                            // Extract image from JSON-LD schema
                            var jsonLdMatch = Regex.Match(responseBody, @"""image"":\s*""([^""]+)""");
                            if (jsonLdMatch.Success)
                            {
                                animeInfo.Image = jsonLdMatch.Groups[1].Value;
                            }
                            else
                            {
                                // Fallback: look for image in HTML
                                var imgNode = htmlDoc.DocumentNode.SelectSingleNode("//img[contains(@src, 'imajlar')]");
                                animeInfo.Image = imgNode != null ? MakeAbsoluteUrl(imgNode.GetAttributeValue("src", "")) : "";
                            }

                            // Extract episode count from "Bölüm Sayısı" section
                            animeInfo.Episodes = new List<AnimeEpisodes>();
                            int totalEpisodes = 1;

                            // Try to extract episode count from HTML table structure
                            // Pattern: Bölüm Sayısı</b></td><td...>:</td><td...>X / Y</td> or similar
                            var episodeCountMatch = Regex.Match(responseBody, @"Bölüm Sayısı[^>]*>.*?</td>.*?</td>.*?>(\d+)(?:\s*/\s*\d+)?", RegexOptions.Singleline);
                            if (episodeCountMatch.Success && int.TryParse(episodeCountMatch.Groups[1].Value, out int episodeCount))
                            {
                                totalEpisodes = episodeCount;
                            }
                            else
                            {
                                // Fallback: try to find episodes by looking for video links
                                var videoLinkMatches = Regex.Matches(responseBody, $@"/video/{Regex.Escape(Query)}-(\d+)");
                                if (videoLinkMatches.Count > 0)
                                {
                                    var maxEpisode = videoLinkMatches
                                        .Cast<Match>()
                                        .Select(m => int.TryParse(m.Groups[1].Value, out int ep) ? ep : 0)
                                        .Max();
                                    if (maxEpisode > 0)
                                    {
                                        totalEpisodes = maxEpisode;
                                    }
                                }
                            }

                            // Create episodes based on the episode count
                            for (int i = 1; i <= totalEpisodes; i++)
                            {
                                animeInfo.Episodes.Add(new AnimeEpisodes
                                {
                                    EpisodeNumber = i,
                                    EpisodeId = $"{Query}-{i}"
                                });
                            }

                            // Set episode count
                            animeInfo.EpisodeCount = totalEpisodes;

                            Debug.WriteLine($"TurkAnime: Extracted info for {animeInfo.Title}");
                            Debug.WriteLine($"  Description: {(string.IsNullOrEmpty(animeInfo.Description) ? "None" : animeInfo.Description.Substring(0, Math.Min(100, animeInfo.Description.Length)))}...");
                            Debug.WriteLine($"  Image: {animeInfo.Image}");
                            Debug.WriteLine($"  Episodes: {animeInfo.Episodes.Count}");
                        }
                        else
                        {
                            Debug.WriteLine($"TurkAnime anime info request failed with status: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error fetching anime info from TurkAnime: {ex.Message}");
                    }
                }
            }

            return JsonSerializer.Serialize(animeInfo);
        }

        public async Task<string> EpisodeStreamLinks(string AnimeId, int Episode)
        {
            var handler = new HttpClientHandler();
            handler.UseCookies = false;
            handler.AutomaticDecompression = ~DecompressionMethods.None;

            List<AnimeStreamingLinks> streamingLinks = new List<AnimeStreamingLinks>();

            using (var httpClient = new HttpClient(handler))
            {
                httpClient.Timeout = TimeSpan.FromSeconds(30);

                string episodeUrl = $"{Host}/video/{AnimeId}-{Episode}";

                using (var request = new HttpRequestMessage(new HttpMethod("GET"), episodeUrl))
                {
                    request.Headers.TryAddWithoutValidation("User-Agent",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
                    request.Headers.TryAddWithoutValidation("Accept",
                        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
                    request.Headers.TryAddWithoutValidation("Accept-Language", "tr-TR,tr;q=0.9,en;q=0.8");
                    request.Headers.TryAddWithoutValidation("Referer", Host);

                    try
                    {
                        var response = await httpClient.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var responseBody = await response.Content.ReadAsStringAsync();
                            HtmlDocument htmlDoc = new HtmlDocument();
                            htmlDoc.LoadHtml(responseBody);

                            // Look for video sources in various formats
                            // Check for iframe sources
                            var iframeNodes = htmlDoc.DocumentNode.SelectNodes("//iframe[@src]");
                            if (iframeNodes != null)
                            {
                                foreach (var iframe in iframeNodes)
                                {
                                    var src = iframe.GetAttributeValue("src", "");
                                    if (!string.IsNullOrEmpty(src) && IsValidVideoSource(src))
                                    {
                                        streamingLinks.Add(new AnimeStreamingLinks
                                        {
                                            Quality = "Default",
                                            Source = MakeAbsoluteUrl(src)
                                        });
                                    }
                                }
                            }

                            // Check for video tags
                            var videoNodes = htmlDoc.DocumentNode.SelectNodes("//video[@src] | //video//source[@src]");
                            if (videoNodes != null)
                            {
                                foreach (var video in videoNodes)
                                {
                                    var src = video.GetAttributeValue("src", "");
                                    if (!string.IsNullOrEmpty(src) && IsValidVideoSource(src))
                                    {
                                        streamingLinks.Add(new AnimeStreamingLinks
                                        {
                                            Quality = "HD",
                                            Source = MakeAbsoluteUrl(src)
                                        });
                                    }
                                }
                            }

                            // Look for JavaScript embedded video URLs
                            var scriptNodes = htmlDoc.DocumentNode.SelectNodes("//script[contains(text(), 'http')]");
                            if (scriptNodes != null)
                            {
                                foreach (var script in scriptNodes)
                                {
                                    var scriptContent = script.InnerText;

                                    // Look for common video URL patterns
                                    var urlMatches = Regex.Matches(scriptContent, @"['""]([^'""]*\.(?:mp4|m3u8|mkv|avi)[^'""]*)['""]");
                                    foreach (Match match in urlMatches)
                                    {
                                        var url = match.Groups[1].Value;
                                        if (IsValidVideoSource(url))
                                        {
                                            streamingLinks.Add(new AnimeStreamingLinks
                                            {
                                                Quality = url.Contains("m3u8") ? "HLS" : "MP4",
                                                Source = MakeAbsoluteUrl(url)
                                            });
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"TurkAnime episode request failed with status: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error fetching episode from TurkAnime: {ex.Message}");
                    }
                }
            }

            // Remove duplicates
            streamingLinks = streamingLinks
                .GroupBy(s => s.Source)
                .Select(g => g.First())
                .ToList();

            return JsonSerializer.Serialize(streamingLinks);
        }

        private bool IsValidVideoSource(string url)
        {
            if (string.IsNullOrEmpty(url)) return false;

            // Check for common video file extensions and streaming formats
            var videoExtensions = new[] { ".mp4", ".m3u8", ".mkv", ".avi", ".webm", ".flv" };
            var lowerUrl = url.ToLower();

            return videoExtensions.Any(ext => lowerUrl.Contains(ext)) ||
                   lowerUrl.Contains("video") ||
                   lowerUrl.Contains("stream");
        }
    }
}
