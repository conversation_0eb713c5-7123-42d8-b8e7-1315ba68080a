using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.VisualTree;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.ViewModels.Anime;
using FluentAvalonia.UI.Controls;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;

namespace Dynamo_Desktop.Views.Anime.Subviews
{
    public partial class IndexSubView : UserControl
    {
        public AnimeProviders Provider { get; set; }
        public IndexSubView()
        {
            InitializeComponent();
           
        }
        public void NavigateToDetails(object sender, TappedEventArgs e)
        {
            try
            {
                var grid = sender as Grid;
                var viewModel = DataContext as IndexViewModel;
                
                if (grid == null || viewModel == null)
                {
                    Debug.WriteLine("Invalid navigation request");
                    return;
                }
                
                var contentControls = grid.Children.OfType<ContentControl>().ToList();
                if (contentControls.Count < 2)
                {
                    Debug.WriteLine("Missing content controls for navigation");
                    return;
                }
                
                string animeId = contentControls[0].Content?.ToString();
                if (string.IsNullOrWhiteSpace(animeId))
                {
                    Debug.WriteLine("Missing anime ID");
                    return;
                }
                
                if (!int.TryParse(contentControls[1].Content?.ToString(), out int episodeNumber))
                {
                    episodeNumber = 1; // Default to episode 1
                }
                
                AnimeIndexToDetailsRouteParams routeParams = new()
                {
                    AnimeId = animeId,
                    EpisodeNumber = episodeNumber,
                    Provider = viewModel.Provider
                };
                
                var frame = this.FindAncestorOfType<Frame>();
                if (frame != null)
                {
                    frame.Navigate(typeof(DetailsSubView), routeParams);
                }
                else
                {
                    Debug.WriteLine("Could not find parent frame for navigation");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to details: {ex.Message}");
            }
        }
    }
}
