using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;

namespace Dynamo_Desktop.Services;

public  class VideoService
{
    public event EventHandler<EventArgs> ProcessExited;

    public VideoService()
    {
        // Constructor - event will be subscribed by caller
    }
    public async Task<bool> Play(string Source = null)
    {
        if (string.IsNullOrWhiteSpace(Source))
        {
            Debug.WriteLine("No video source provided");
            return false;
        }

        // Validate URL
        if (!IsValidUrl(Source))
        {
            Debug.WriteLine($"Invalid URL provided: {Source}");
            return false;
        }

        Debug.WriteLine($"Playing video: {Source}");

        try
        {
            string media_player = SettingsService.GetSettings().media_player;
            bool success = false;

            switch (media_player)
            {
                case "vlc":
                    success = await PlayWithVlc(Source);
                    if (!success)
                    {
                        Debug.WriteLine("VLC failed, trying mpv as fallback");
                        success = await PlayWithMpv(Source);
                    }
                    break;
                case "mpv":
                    success = await PlayWithMpv(Source);
                    if (!success)
                    {
                        Debug.WriteLine("mpv failed, trying VLC as fallback");
                        success = await PlayWithVlc(Source);
                    }
                    break;
                default:
                    success = await PlayWithMpv(Source);
                    if (!success)
                    {
                        Debug.WriteLine("mpv failed, trying VLC as fallback");
                        success = await PlayWithVlc(Source);
                    }
                    break;
            }

            if (!success)
            {
                Debug.WriteLine("All media players failed, trying system default");
                success = await PlayWithSystemDefault(Source);
            }

            return success;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error playing video: {ex.Message}");
            return false;
        }
    }

    private bool IsValidUrl(string url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return false;

        return Uri.TryCreate(url, UriKind.Absolute, out Uri result)
            && (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
    private async Task<bool> PlayWithMpv(string source = null)
    {
        try
        {
            // Try multiple possible mpv locations
            string[] mpvPaths = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? new[] {
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "mpv", "mpv.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "mpv", "mpv.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "mpv", "mpv.exe"),
                    "mpv.exe" // Try system PATH
                }
                : new[] { "mpv" };

            string mpvPath = null;
            foreach (var path in mpvPaths)
            {
                if (File.Exists(path) || path == "mpv" || path == "mpv.exe")
                {
                    mpvPath = path;
                    break;
                }
            }

            if (mpvPath == null)
            {
                Debug.WriteLine("mpv not found in any expected location");
                return false;
            }

            Debug.WriteLine($"Using mpv at: {mpvPath}");

            var proc = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = mpvPath,
                    Arguments = $"\"{source}\" --force-window=immediate --no-terminal",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                },
                EnableRaisingEvents = true
            };

            proc.Exited += (sender, args) =>
            {
                ProcessExited?.Invoke(this, EventArgs.Empty);
            };

            proc.Start();
            Debug.WriteLine($"Started mpv process with PID: {proc.Id}");

            // Wait a bit to see if the process starts successfully
            await Task.Delay(1000);

            if (proc.HasExited && proc.ExitCode != 0)
            {
                Debug.WriteLine($"mpv exited with error code: {proc.ExitCode}");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error starting mpv: {ex.Message}");
            return false;
        }
    }

    private async Task<bool> PlayWithVlc(string source = null)
    {
        try
        {
            string vlcPath = null;

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                // Try multiple VLC installation paths
                string[] vlcPaths = {
                    "C:\\Program Files\\VideoLAN\\VLC\\vlc.exe",
                    "C:\\Program Files (x86)\\VideoLAN\\VLC\\vlc.exe",
                    "vlc.exe" // Try system PATH
                };

                foreach (var path in vlcPaths)
                {
                    if (File.Exists(path) || path == "vlc.exe")
                    {
                        vlcPath = path;
                        break;
                    }
                }
            }
            else
            {
                vlcPath = "vlc"; // Assume it's in PATH on Linux/Mac
            }

            if (vlcPath == null)
            {
                Debug.WriteLine("VLC not found in any expected location");
                return false;
            }

            Debug.WriteLine($"Using VLC at: {vlcPath}");

            var proc = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = vlcPath,
                    Arguments = $"\"{source}\" --intf dummy --play-and-exit",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                },
                EnableRaisingEvents = true
            };

            proc.Exited += (sender, args) =>
            {
                ProcessExited?.Invoke(this, EventArgs.Empty);
            };

            proc.Start();
            Debug.WriteLine($"Started VLC process with PID: {proc.Id}");

            // Wait a bit to see if the process starts successfully
            await Task.Delay(1000);

            if (proc.HasExited && proc.ExitCode != 0)
            {
                Debug.WriteLine($"VLC exited with error code: {proc.ExitCode}");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error starting VLC: {ex.Message}");
            return false;
        }
    }

    private async Task<bool> PlayWithSystemDefault(string source = null)
    {
        try
        {
            Debug.WriteLine("Attempting to play with system default application");

            var proc = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = source,
                    UseShellExecute = true, // This will use the system default application
                },
                EnableRaisingEvents = true
            };

            proc.Exited += (sender, args) =>
            {
                ProcessExited?.Invoke(this, EventArgs.Empty);
            };

            proc.Start();
            Debug.WriteLine($"Started system default application with PID: {proc.Id}");

            // Wait a bit to see if the process starts successfully
            await Task.Delay(1000);

            if (proc.HasExited && proc.ExitCode != 0)
            {
                Debug.WriteLine($"System default application exited with error code: {proc.ExitCode}");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error starting system default application: {ex.Message}");
            return false;
        }
    }
}
