using System.Diagnostics;
using Dynamo_Desktop.Models.Anime;
using Dynamo_Desktop.Scrapers.Anime;
using Dynamo_Desktop.Services.Anime;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Dynamo_Desktop.Tests;

[TestClass]
public class EnhancedTurkAnimeTests
{
    private EnhancedTurkAnimeScraper _scraper = null!;
    private EnhancedTurkAnimeService _service = null!;
    
    [TestInitialize]
    public void Setup()
    {
        _scraper = new EnhancedTurkAnimeScraper();
        _service = new EnhancedTurkAnimeService();
    }
    
    [TestCleanup]
    public void Cleanup()
    {
        _scraper?.Dispose();
        _service?.Dispose();
    }
    
    [TestMethod]
    public async Task TestEnhancedRecentAnime()
    {
        // Test recent anime fetching with enhanced scraper
        var response = JsonSerializer.Deserialize<List<PopularAnime>>(
            await _scraper.RecentAnime(Page: 1));
        
        Assert.IsNotNull(response);
        Assert.IsInstanceOfType<List<PopularAnime>>(response);
        
        Debug.WriteLine($"Enhanced scraper found {response.Count} recent anime");
        foreach (var anime in response.Take(3))
        {
            Debug.WriteLine($"Anime: {anime.Title} (ID: {anime.AnimeId})");
            Assert.IsFalse(string.IsNullOrEmpty(anime.Title));
            Assert.IsFalse(string.IsNullOrEmpty(anime.AnimeId));
        }
    }
    
    [TestMethod]
    public async Task TestEnhancedSearch()
    {
        // Test search functionality with enhanced scraper
        var searchTerm = "naruto";
        var response = JsonSerializer.Deserialize<List<PopularAnime>>(
            await _scraper.Search(searchTerm));
        
        Assert.IsNotNull(response);
        Assert.IsInstanceOfType<List<PopularAnime>>(response);
        
        Debug.WriteLine($"Enhanced scraper found {response.Count} results for '{searchTerm}'");
        foreach (var anime in response.Take(3))
        {
            Debug.WriteLine($"Search result: {anime.Title} (ID: {anime.AnimeId})");
            Assert.IsFalse(string.IsNullOrEmpty(anime.Title));
            Assert.IsFalse(string.IsNullOrEmpty(anime.AnimeId));
        }
    }
    
    [TestMethod]
    public async Task TestEnhancedAnimeInfo()
    {
        // First get a recent anime to test info retrieval
        var recentResponse = JsonSerializer.Deserialize<List<PopularAnime>>(
            await _scraper.RecentAnime(Page: 1));
        
        Assert.IsNotNull(recentResponse);
        Assert.IsTrue(recentResponse.Count > 0);
        
        var firstAnime = recentResponse.First();
        var infoResponse = JsonSerializer.Deserialize<AnimeInfo>(
            await _scraper.AnimeInfo(firstAnime.AnimeId));
        
        Assert.IsNotNull(infoResponse);
        Assert.IsFalse(string.IsNullOrEmpty(infoResponse.Title));
        
        Debug.WriteLine($"Enhanced scraper anime info:");
        Debug.WriteLine($"  Title: {infoResponse.Title}");
        Debug.WriteLine($"  Description: {(string.IsNullOrEmpty(infoResponse.Description) ? "N/A" : infoResponse.Description.Substring(0, Math.Min(100, infoResponse.Description.Length)))}...");
        Debug.WriteLine($"  Episodes: {infoResponse.Episodes?.Count ?? 0}");
        Debug.WriteLine($"  Image: {infoResponse.Image}");
    }
    
    [TestMethod]
    public async Task TestEnhancedStreamingLinks()
    {
        // First get a recent anime to test streaming links
        var recentResponse = JsonSerializer.Deserialize<List<PopularAnime>>(
            await _scraper.RecentAnime(Page: 1));
        
        Assert.IsNotNull(recentResponse);
        Assert.IsTrue(recentResponse.Count > 0);
        
        var firstAnime = recentResponse.First();
        var linksResponse = JsonSerializer.Deserialize<List<AnimeStreamingLinks>>(
            await _scraper.EpisodeStreamLinks(firstAnime.AnimeId, 1));
        
        Assert.IsNotNull(linksResponse);
        
        Debug.WriteLine($"Enhanced scraper found {linksResponse.Count} streaming links for {firstAnime.Title} Episode 1");
        foreach (var link in linksResponse.Take(3))
        {
            Debug.WriteLine($"  Quality: {link.Quality}, Source: {(string.IsNullOrEmpty(link.Source) ? "N/A" : link.Source.Substring(0, Math.Min(50, link.Source.Length)))}...");
            Assert.IsFalse(string.IsNullOrEmpty(link.Source));
        }
    }
    
    [TestMethod]
    public async Task TestEnhancedServiceRecentAnime()
    {
        // Test the enhanced service layer
        var response = await _service.RecentAnime(Page: 1);
        
        Assert.IsNotNull(response);
        Assert.IsInstanceOfType<List<PopularAnime>>(response);
        
        Debug.WriteLine($"Enhanced service found {response.Count} recent anime");
        foreach (var anime in response.Take(3))
        {
            Debug.WriteLine($"Service result: {anime.Title} (ID: {anime.AnimeId})");
            Assert.IsFalse(string.IsNullOrEmpty(anime.Title));
            Assert.IsFalse(string.IsNullOrEmpty(anime.AnimeId));
        }
    }
    
    [TestMethod]
    public async Task TestEnhancedServiceSearch()
    {
        // Test the enhanced service search
        var searchTerm = "one piece";
        var response = await _service.Search(searchTerm);
        
        Assert.IsNotNull(response);
        Assert.IsInstanceOfType<List<PopularAnime>>(response);
        
        Debug.WriteLine($"Enhanced service found {response.Count} results for '{searchTerm}'");
        foreach (var anime in response.Take(3))
        {
            Debug.WriteLine($"Service search result: {anime.Title} (ID: {anime.AnimeId})");
            Assert.IsFalse(string.IsNullOrEmpty(anime.Title));
            Assert.IsFalse(string.IsNullOrEmpty(anime.AnimeId));
        }
    }
    
    [TestMethod]
    public async Task TestCachingBehavior()
    {
        // Test that caching works by making the same request twice
        var stopwatch = Stopwatch.StartNew();
        
        // First request (should hit the network)
        var firstResponse = await _service.RecentAnime(Page: 1);
        var firstRequestTime = stopwatch.ElapsedMilliseconds;
        
        stopwatch.Restart();
        
        // Second request (should hit the cache)
        var secondResponse = await _service.RecentAnime(Page: 1);
        var secondRequestTime = stopwatch.ElapsedMilliseconds;
        
        Assert.IsNotNull(firstResponse);
        Assert.IsNotNull(secondResponse);
        Assert.AreEqual(firstResponse.Count, secondResponse.Count);
        
        Debug.WriteLine($"First request took: {firstRequestTime}ms");
        Debug.WriteLine($"Second request took: {secondRequestTime}ms");
        
        // Second request should be significantly faster due to caching
        Assert.IsTrue(secondRequestTime < firstRequestTime / 2, 
            "Second request should be much faster due to caching");
    }
    
    [TestMethod]
    public async Task TestErrorHandling()
    {
        // Test error handling with invalid anime ID
        var invalidId = "this-anime-does-not-exist-12345";
        var infoResponse = await _service.Info(invalidId);
        
        Assert.IsNotNull(infoResponse);
        // Should return empty info but not throw exception
        
        var linksResponse = await _service.StreamingLinks(invalidId, 1);
        Assert.IsNotNull(linksResponse);
        // Should return empty list but not throw exception
        
        Debug.WriteLine("Error handling test passed - no exceptions thrown for invalid data");
    }
    
    [TestMethod]
    public async Task TestRateLimiting()
    {
        // Test that rate limiting works by making multiple rapid requests
        var tasks = new List<Task<List<PopularAnime>>>();
        
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_service.RecentAnime(Page: 1));
        }
        
        var stopwatch = Stopwatch.StartNew();
        var results = await Task.WhenAll(tasks);
        stopwatch.Stop();
        
        Assert.IsTrue(results.All(r => r != null));
        Debug.WriteLine($"5 concurrent requests completed in {stopwatch.ElapsedMilliseconds}ms");
        
        // Should take some time due to rate limiting
        Assert.IsTrue(stopwatch.ElapsedMilliseconds > 1000, 
            "Rate limiting should prevent requests from completing too quickly");
    }
}
